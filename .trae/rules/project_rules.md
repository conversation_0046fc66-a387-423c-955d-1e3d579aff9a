MCP stands for Model Context Protokoll and contains tools for Language Models.

Projektzusammenfassung: Webanwendung zur Energieausweiserstellung
Projektziel: Entwicklung einer Webanwendung, die es Endanwendern ermöglicht, Daten für einen Energieausweis über eine Reihe von Online-Formularen einzugeben. Nach der Eingabe erhalten die Nutzer eine tabellarische Zusammenfassung und können den Energieausweis über eine Stripe-Checkout-Integration "kaufen". Administratoren erhalten ein separates Dashboard zur Datenverwaltung und zum CSV-Export der erfassten Energieausweisdaten.

Kernfunktionalitäten:

Dateneingabe durch Nutzer:
Umfangreiche Formulare zur Erfassung aller relevanten Gebäudedaten, Kundendaten, Heizungs-, Trinkwarmwasser-, Lüftungs- und Verbrauchsdaten gemäß der bereitgestellten Schnittstellenbeschreibung-Import-Energieausweise.pdf ([1], [2], [3], [4], [5], [6], [7], [8]).
Upload-Möglichkeit für Gebäudebilder und die letzten drei Verbrauchsrechnungen.
Datenzusammenfassung:
Anzeige einer tabellarischen Übersicht aller eingegebenen Daten vor dem Kauf.
Kaufabwicklung:
Integration von Stripe Checkout zur Bezahlung des Energieausweises.
Benutzerauthentifizierung:
Login/Logout-Funktionalität für Endanwender und Administratoren mittels Supabase Auth.
Geschützte Routen für Dateneingabe und Admin-Bereich.
Admin-Dashboard:
Anzeige der erfassten Energieausweis-Datensätze.
Funktion zum Export der Daten als CSV-Datei (Format: UTF-8 ohne BOM, Semikolon-Trenner, gemäß Schnittstellenbeschreibung) ([1]).
Datenspeicherung (Backend):
Supabase für die Persistierung der Energieausweisdaten (nach Zahlung).
Supabase Storage für hochgeladene Bilder und Verbrauchsrechnungen.
Technische Spezifikationen (Tech-Stack):

Frontend-Framework: React.js
Programmiersprache: TypeScript
Build-Tool: Vite
Styling: Tailwind CSS
Routing: Tanstack Router
State Management & Datenabruf: Tanstack Query
Formular-Management: Tanstack Forms
Backend-as-a-Service (BaaS): Supabase (Authentifizierung, Datenbank, Storage)
Zahlungsdienstleister: Stripe (Checkout)
Wichtige Referenzen:

Schnittstellenbeschreibung: Das Dokument Schnittstellenbeschreibung-Import-Energieausweise.pdf ([1], [2], [3], [4], [5], [6], [7], [8]) ist die maßgebliche Quelle für Feldnamen, Datenstrukturen, Auswahlwerte und das Format der zu generierenden CSV-Datei ([1]).
Bestehendes Supabase-Projekt: Ein vorkonfiguriertes Supabase-Projekt mit den notwendigen Tabellen ist bereits vorhanden und soll genutzt werden.
Entwicklungsansatz:

Iterative Entwicklung, aufgeteilt in kleinere, logische Schritte (z.B. Grundgerüst, Authentifizierung, einzelne Formularseiten, Zusammenfassung, Bezahlung, Admin-Funktionen).
Fokus auf eine benutzerfreundliche Oberfläche und robuste Datenverarbeitung.