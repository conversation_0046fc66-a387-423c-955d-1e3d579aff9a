# FensterPage Testing Guide

## Overview
This document provides comprehensive testing instructions for the modified FensterPage component, which has been updated to remove manual window addition functionality while preserving the automated synchronization with wall data.

## Changes Made
- ❌ **Removed**: Manual window addition ("Zusätzliches Fenster hinzufügen" button)
- ❌ **Removed**: Manual window removal (individual remove buttons)
- ❌ **Removed**: Manual window management functions (`addFenster`, `removeFenster`)
- ✅ **Preserved**: Automatic window synchronization with wall data
- ✅ **Preserved**: All form validation and data persistence
- ✅ **Preserved**: Navigation flow and page transitions

## Prerequisites
- Access to a certificate with type 'WG/B' (FensterPage is only available for WG/B certificates)
- Browser with developer tools (for debugging if needed)
- Test account with appropriate permissions

## Test Cases

### Test Case 1: Basic Sync Functionality
**Objective**: Verify that windows are automatically created based on wall definitions

**Steps**:
1. Navigate to `/erfassen/gebaeudeform`
2. Add 3 walls with names:
   - "Außenwand Nord"
   - "Außenwand Süd" 
   - "Außenwand Ost"
3. Navigate to `/erfassen/fenster`
4. Verify that 3 windows are automatically created with matching names
5. Confirm all windows show "Automatisch synchronisiert" badge
6. Verify window names are read-only (grayed out input fields)

**Expected Results**:
- ✅ 3 windows created automatically
- ✅ Window names match wall names exactly
- ✅ All windows show sync status badge
- ✅ Window names are non-editable
- ✅ Other window properties (area, type, orientation) are editable

### Test Case 2: No Manual Addition Controls
**Objective**: Confirm manual addition functionality is completely removed

**Steps**:
1. Navigate to `/erfassen/fenster` with existing windows
2. Inspect the page for any manual addition controls
3. Check browser console for any JavaScript errors
4. Verify no "Zusätzliches Fenster hinzufügen" button exists
5. Verify no individual remove buttons on window cards

**Expected Results**:
- ❌ No "Add Window" button visible
- ❌ No individual remove buttons on windows
- ❌ No manual addition functions accessible
- ✅ No JavaScript errors in console
- ✅ Clean, simplified interface

### Test Case 3: Wall Changes Sync
**Objective**: Verify that changes to walls automatically update windows

**Steps**:
1. Create 2 walls: "Wall A" and "Wall B"
2. Navigate to FensterPage, confirm 2 windows exist
3. Go back to GebaeudeformPage
4. Rename "Wall A" to "Updated Wall A"
5. Add a new wall "Wall C"
6. Remove "Wall B"
7. Return to FensterPage

**Expected Results**:
- ✅ Window formerly named "Wall A" now shows "Updated Wall A"
- ✅ New window "Wall C" is automatically created
- ✅ Window "Wall B" is automatically removed
- ✅ User edits to window properties (area, type) are preserved
- ✅ Sync status shows correct count

### Test Case 4: No Walls Scenario
**Objective**: Test behavior when no walls are defined

**Steps**:
1. Ensure no walls are defined in GebaeudeformPage
2. Navigate to FensterPage
3. Verify appropriate messaging is shown
4. Confirm no windows are displayed
5. Verify navigation still works correctly

**Expected Results**:
- ✅ Warning message about no walls defined
- ✅ Guidance to define walls first
- ✅ No window cards displayed
- ✅ Navigation buttons still functional
- ❌ No error messages or crashes

### Test Case 5: Form Validation
**Objective**: Ensure form validation still works correctly

**Steps**:
1. Create windows via wall sync
2. Leave required fields empty (e.g., "Fläche")
3. Try to submit the form
4. Fill in required fields with valid data
5. Submit successfully

**Expected Results**:
- ❌ Form submission blocked with empty required fields
- ✅ Clear validation error messages shown
- ✅ Successful submission with valid data
- ✅ Navigation to next page (HeizungPage)

### Test Case 6: Data Persistence
**Objective**: Verify that window data is properly saved and loaded

**Steps**:
1. Create windows via wall sync
2. Edit window properties (area, type, orientation, build year)
3. Navigate away from the page
4. Return to FensterPage
5. Verify all edits are preserved

**Expected Results**:
- ✅ All user edits are saved to database
- ✅ Data loads correctly on page return
- ✅ Sync status remains consistent
- ✅ No data loss occurs

### Test Case 7: UI/UX Consistency
**Objective**: Verify the updated interface is consistent and user-friendly

**Steps**:
1. Review all informational text and messages
2. Check that sync indicators are appropriate
3. Verify color coding and visual hierarchy
4. Test responsive design on different screen sizes
5. Confirm accessibility features work

**Expected Results**:
- ✅ Clear, consistent messaging about automatic management
- ✅ Appropriate use of blue color for sync indicators
- ✅ No references to manual addition in text
- ✅ Responsive design works on mobile/tablet
- ✅ Keyboard navigation functional

## Edge Cases to Test

### Edge Case 1: Rapid Navigation
- Navigate quickly between GebaeudeformPage and FensterPage
- Verify sync process handles rapid navigation gracefully
- Check for race conditions or loading states

### Edge Case 2: Large Number of Walls
- Create 20+ walls in GebaeudeformPage
- Verify FensterPage handles many windows efficiently
- Check for performance issues or UI problems

### Edge Case 3: Special Characters in Wall Names
- Create walls with special characters (ä, ö, ü, ß, spaces, numbers)
- Verify window names sync correctly
- Check for encoding or display issues

### Edge Case 4: Network Issues
- Simulate network interruption during sync
- Verify error handling is graceful
- Check that retry mechanisms work

## Browser Compatibility
Test in the following browsers:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Performance Benchmarks
- Page load time should be < 2 seconds
- Sync operation should complete < 1 second
- No memory leaks during navigation
- Smooth animations and transitions

## Regression Testing Checklist
- [ ] Sync functionality works correctly
- [ ] No manual addition controls present
- [ ] Form validation functions properly
- [ ] Data persistence works
- [ ] Navigation flow intact
- [ ] No JavaScript errors
- [ ] Responsive design maintained
- [ ] Accessibility features functional
- [ ] Performance acceptable
- [ ] All edge cases handled

## Troubleshooting

### Common Issues
1. **Windows not syncing**: Check if walls are properly saved in GebaeudeformPage
2. **Sync status not updating**: Clear browser cache and reload
3. **Form validation errors**: Verify all required fields have valid data
4. **Navigation issues**: Check certificate type is WG/B

### Debug Information
- Check browser console for errors
- Verify network requests in DevTools
- Check Supabase database for data consistency
- Review component state in React DevTools

## Sign-off Criteria
- [ ] All test cases pass
- [ ] No critical bugs identified
- [ ] Performance meets requirements
- [ ] User experience is intuitive
- [ ] Documentation is complete

---

**Last Updated**: [Current Date]
**Tested By**: [Tester Name]
**Status**: Ready for Testing