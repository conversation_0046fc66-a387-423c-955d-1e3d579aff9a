# Performance Optimizations for ZusammenfassungPage Re-render Issues

## Problem Analysis

The excessive re-renders when switching browser windows/tabs were caused by several cascading issues:

1. **AuthProvider re-rendering twice** due to Supabase auth state changes on window focus
2. **useCertificateFiles hook being called repeatedly** with the same certificate ID
3. **Router context updates** triggering downstream re-renders
4. **Ineffective memoization** that wasn't preventing upstream re-renders
5. **Query refetching on window focus** causing unnecessary data fetches

## Root Causes Identified

### 1. AuthContext Issues
- Supabase auth state changes were firing rapidly on window focus
- No debouncing of auth state changes
- Functions weren't memoized, causing context value recreation
- Excessive console logging on every render

### 2. CertificateContext Issues
- Functions weren't memoized
- Context value wasn't memoized
- Excessive console logging

### 3. useCertificateFiles Hook Issues
- User query was refetching on window focus
- Hook was logging on every call instead of only on changes
- Certificate files query was refetching on window focus

### 4. App.tsx Router Updates
- Router context wasn't memoized
- Dependencies were too broad, causing unnecessary updates

### 5. ZusammenfassungPage Query Issues
- Main energieausweis query was refetching on window focus
- No stale time or cache time configured

## Optimizations Implemented

### 1. AuthContext Optimizations (`src/contexts/AuthContext.tsx`)

**Added debouncing to auth state changes:**
```typescript
// Debounce rapid auth state changes (common on window focus)
timeoutId = setTimeout(() => {
  if (!mounted) return;

  setSession(session);
  setUser(session?.user ?? null);
  setIsAnonymous(session?.user?.is_anonymous ?? false);
  setLoading(false);
}, 50); // 50ms debounce
```

**Memoized all functions and context value:**
```typescript
const signIn = useCallback(async (email: string, password: string) => {
  // ... implementation
}, []);

const value = useMemo(() => ({
  session, user, loading, isAnonymous,
  signIn, signUp, signInAnonymously, signOut,
  resetPassword, updatePassword, linkIdentity,
}), [session, user, loading, isAnonymous, signIn, signUp, signInAnonymously, signOut, resetPassword, updatePassword, linkIdentity]);
```

### 2. CertificateContext Optimizations (`src/contexts/CertificateContext.tsx`)

**Memoized functions and context value:**
```typescript
const createNewCertificate = useCallback(async (certificateType: string): Promise<string> => {
  // ... implementation
}, []);

const contextValue: CertificateContextType = useMemo(() => ({
  activeCertificateId, setActiveCertificateId,
  createNewCertificate, getCertificateById, loading,
}), [activeCertificateId, loading, createNewCertificate, getCertificateById]);
```

### 3. useCertificateFiles Hook Optimizations (`src/hooks/useCertificateFiles.ts`)

**Enhanced user query caching:**
```typescript
const { data: user } = useQuery({
  queryKey: ['user'],
  queryFn: async () => {
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  },
  staleTime: 10 * 60 * 1000, // Increased to 10 minutes
  gcTime: 30 * 60 * 1000, // Keep in cache for 30 minutes
  refetchOnWindowFocus: false, // Prevent refetch on window focus
  refetchOnMount: false, // Only refetch if stale
  refetchOnReconnect: false, // Don't refetch on network reconnect
});
```

**Optimized logging to only trigger on actual changes:**
```typescript
const prevCertificateIdRef = useMemo(() => ({ current: certificateId }), []);
useEffect(() => {
  if (prevCertificateIdRef.current !== certificateId) {
    console.log('📁 useCertificateFiles certificateId changed:', certificateId);
    prevCertificateIdRef.current = certificateId;
  }
}, [certificateId]);
```

### 4. App.tsx Router Context Optimizations (`src/App.tsx`)

**Memoized router context with specific dependencies:**
```typescript
const routerContext = useMemo(() => ({
  user, session, loading,
}), [user?.id, session?.access_token, loading]); // More specific dependencies

useEffect(() => {
  if (!loading) {
    console.log('Updating router context with:', routerContext);
    router.update({ context: routerContext });
  }
}, [routerContext, loading]);
```

### 5. ZusammenfassungPage Query Optimizations (`src/pages/erfassen/ZusammenfassungPage.tsx`)

**Enhanced query caching configuration:**
```typescript
const { data: energieausweisData, isError, isLoading: queryLoading } = useQuery({
  queryKey: ['energieausweise', 'all', memoizedCertificateId],
  queryFn: async () => { /* ... */ },
  enabled: !!memoizedCertificateId,
  retry: false,
  staleTime: 2 * 60 * 1000, // 2 minutes - prevent unnecessary refetches
  gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  refetchOnWindowFocus: false, // Prevent refetch on window focus - key optimization
  refetchOnMount: false, // Only refetch if data is stale
  refetchOnReconnect: false, // Don't refetch on network reconnect
});
```

**Optimized console logging:**
```typescript
const renderCount = useMemo(() => Date.now(), [activeCertificateId, error, isProcessingPayment]);
useEffect(() => {
  console.log(`🏠 ZusammenfassungPage state changed ${renderCount}:`, {
    hasError: !!error, isProcessingPayment, activeCertificateId, isAnonymous
  });
}, [renderCount, error, isProcessingPayment, activeCertificateId, isAnonymous]);
```

## Expected Results

After these optimizations, you should see:

1. **Significantly reduced re-renders** when switching browser windows/tabs
2. **Fewer console logs** - only when meaningful state changes occur
3. **No unnecessary query refetches** on window focus
4. **Improved performance** due to better memoization and caching
5. **More stable component behavior** with debounced auth state changes

## Key Optimization Principles Applied

1. **Debouncing rapid state changes** (especially auth state)
2. **Memoizing context values and functions** to prevent recreation
3. **Disabling refetchOnWindowFocus** for stable queries
4. **Using more specific dependencies** in useMemo and useEffect
5. **Implementing proper cleanup** in useEffect hooks
6. **Optimizing console logging** to reduce noise

## Testing Recommendations

1. Open the ZusammenfassungPage in your browser
2. Switch to another tab/window and back
3. Monitor the console for reduced logging
4. Verify that queries don't refetch unnecessarily
5. Check that the page remains responsive and functional

These optimizations should resolve the excessive re-render issues while maintaining all functionality.
