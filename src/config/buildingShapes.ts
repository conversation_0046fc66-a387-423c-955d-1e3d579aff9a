// Building shape configuration for energy certificate wall configuration
import type { CertificateType } from '../hooks/useCertificateType';

export interface WallTemplate {
  id: string;
  bezeichnung: string;
  position: string;
  defaultMassiv: string;
}

export interface BuildingShape {
  id: string;
  name: string;
  description: string;
  walls: WallTemplate[];
  image: string;
  certificateTypes: CertificateType[];
}

export const buildingShapes: BuildingShape[] = [
  {
    id: 'form-a',
    name: 'Form a) Rechteckiges Gebäude',
    description: 'Einfaches rechteckiges Gebäude mit 4 Außenwänden',
    walls: [
      { 
        id: 'wall-a', 
        bezeichnung: 'Außenwand a', 
        position: 'south', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-b', 
        bezeichnung: 'Außenwand b', 
        position: 'east', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-c', 
        bezeichnung: 'Außenwand c', 
        position: 'north', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-d', 
        bezeichnung: 'Außenwand d', 
        position: 'west', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      }
    ],
    image: '/images/building-shapes/form-a.svg',
    certificateTypes: ['WG/B']
  },
  {
    id: 'form-b',
    name: 'Form b) L-förmiges Gebäude',
    description: 'L-förmiges Gebäude mit 6 Außenwänden',
    walls: [
      { 
        id: 'wall-a', 
        bezeichnung: 'Außenwand a', 
        position: 'south', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-b', 
        bezeichnung: 'Außenwand b', 
        position: 'east', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-c', 
        bezeichnung: 'Außenwand c', 
        position: 'north-east', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-d', 
        bezeichnung: 'Außenwand d', 
        position: 'north', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-e', 
        bezeichnung: 'Außenwand e', 
        position: 'center', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-f', 
        bezeichnung: 'Außenwand f', 
        position: 'west', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      }
    ],
    image: '/images/building-shapes/form-b.svg',
    certificateTypes: ['WG/B']
  },
  {
    id: 'form-c',
    name: 'Form c) T-förmiges Gebäude',
    description: 'T-förmiges Gebäude mit 8 Außenwänden',
    walls: [
      { 
        id: 'wall-a', 
        bezeichnung: 'Außenwand a', 
        position: 'south', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-b', 
        bezeichnung: 'Außenwand b', 
        position: 'east', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-c', 
        bezeichnung: 'Außenwand c', 
        position: 'north-east', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-d', 
        bezeichnung: 'Außenwand d', 
        position: 'north-center', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-e', 
        bezeichnung: 'Außenwand e', 
        position: 'north-west', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-f', 
        bezeichnung: 'Außenwand f', 
        position: 'west', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-g', 
        bezeichnung: 'Außenwand g', 
        position: 'center-west', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-h', 
        bezeichnung: 'Außenwand h', 
        position: 'center-south', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      }
    ],
    image: '/images/building-shapes/form-c.svg',
    certificateTypes: ['WG/B']
  },
  {
    id: 'form-d',
    name: 'Form d) Komplexes Gebäude',
    description: 'Komplexes Gebäude mit 8 Außenwänden',
    walls: [
      { 
        id: 'wall-a', 
        bezeichnung: 'Außenwand a', 
        position: 'south', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-b', 
        bezeichnung: 'Außenwand b', 
        position: 'east', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-c', 
        bezeichnung: 'Außenwand c', 
        position: 'north-east', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-d', 
        bezeichnung: 'Außenwand d', 
        position: 'north', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-e', 
        bezeichnung: 'Außenwand e', 
        position: 'center', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-f', 
        bezeichnung: 'Außenwand f', 
        position: 'west', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-g', 
        bezeichnung: 'Außenwand g', 
        position: 'south-west', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      },
      { 
        id: 'wall-h', 
        bezeichnung: 'Außenwand h', 
        position: 'south-center', 
        defaultMassiv: 'kb_zweischaligOhneDaemm' 
      }
    ],
    image: '/images/building-shapes/form-d.svg',
    certificateTypes: ['WG/B']
  }
];

/**
 * Get building shape by ID
 */
export const getBuildingShapeById = (id: string): BuildingShape | undefined => {
  return buildingShapes.find(shape => shape.id === id);
};

/**
 * Get building shapes for a specific certificate type
 */
export const getBuildingShapesForCertificateType = (certificateType: CertificateType): BuildingShape[] => {
  return buildingShapes.filter(shape => shape.certificateTypes.includes(certificateType));
};
