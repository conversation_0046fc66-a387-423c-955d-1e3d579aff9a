import { useState, useEffect, useCallback, useMemo } from "react";
import { useForm, useField } from "@tanstack/react-form";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { z } from "zod";
import { Link, useNavigate } from "@tanstack/react-router";
import { supabase } from "../../lib/supabase";
import { useCertificate } from "../../contexts/CertificateContext";
import { Breadcrumb } from "../../components/ui/Breadcrumb";
import { usePageVisit } from "../../hooks/usePageVisit";
import { useNavigationState } from "../../hooks/useNavigationState";
import { ValidationErrorSummary } from "../../components/ui/ValidationErrorSummary";
import {
  handleZodValidationError,
  clearFormFieldErrors,
} from "../../utils/formValidation";

// Type definitions for walls from GebaeudeformPage
interface Wall {
  id: string;
  bezeichnung: string;
  massiv?: string;
  uebergang?: string;
  flaeche: string;
  uWert?: string;
  wallWidth?: string;
  numberOfLevels?: string;
}

// Define the Fenster schema for reuse
const fensterSchema = z.object({
  id: z.string(),
  bezeichnung: z.string().min(1, "Bezeichnung ist erforderlich"),
  art: z.string().optional(),
  flaeche: z.string().min(1, "Fläche ist erforderlich"),
  uWert: z.string().optional(),
  ausrichtung: z.string().optional(),
  baujahr: z.string().optional(),
});

// Define the form schema using Zod
const fensterPageSchema = z.object({
  // Fenster (dynamisch)
  fenster: z.array(fensterSchema).default([]),
});

type FensterFormValues = z.infer<typeof fensterPageSchema>;
type Fenster = z.infer<typeof fensterSchema>;

/**
 * FensterPage Component - Manages window data entry with automatic synchronization
 *
 * This component implements a sophisticated synchronization mechanism between walls
 * (defined in GebaeudeformPage) and windows:
 *
 * SYNCHRONIZATION FEATURES:
 * 1. Automatic Window Creation: For each wall defined in GebaeudeformPage,
 *    a corresponding window is automatically created with the same name
 *
 * 2. Dynamic Updates: When walls are added, removed, or renamed on GebaeudeformPage,
 *    the windows are automatically synchronized on navigation to this page
 *
 * 3. Bidirectional Consistency: Window names are kept in sync with wall names,
 *    but other window properties (area, type, etc.) remain user-editable
 *
 * 4. Automated Window Management: Windows are automatically managed through
 *    synchronization with wall data only
 *
 * TECHNICAL IMPLEMENTATION:
 * - Fetches wall data from gebaeudedetails2.waende
 * - Matches existing windows to walls by bezeichnung (name)
 * - Creates new windows for new walls, preserves existing customizations
 * - Removes windows when corresponding walls are deleted
 * - Visual indicators show which windows are synchronized vs. manually added
 *
 * TESTING THE FEATURE:
 * 1. Go to GebaeudeformPage and add/edit walls with specific names
 * 2. Navigate to FensterPage - windows should auto-create with matching names
 * 3. Edit window properties (area, type) - these should be preserved
 * 4. Go back to GebaeudeformPage and rename a wall
 * 5. Return to FensterPage - window name should update, other properties preserved
 * 6. All windows are managed automatically through synchronization
 */
export const FensterPage = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string[]>
  >({});
  const [isLoading, setIsLoading] = useState(true);
  const { activeCertificateId } = useCertificate();
  const { markPageAsVisited } = useNavigationState("WG/B"); // FensterPage is only for WG/B certificates

  // Mark this page as visited for navigation tracking
  usePageVisit("fenster");

  // Default Fenster - memoized to prevent useEffect dependency changes
  const defaultFenster: Fenster[] = useMemo(
    () => [
      {
        id: "1",
        bezeichnung: "Fenster1",
        art: "fb_KunststoffWSG",
        flaeche: "",
        uWert: "",
        ausrichtung: "0",
        baujahr: "",
      },
    ],
    [],
  );

  // State für dynamische Fenster
  const [fenster, setFenster] = useState<Fenster[]>(defaultFenster);
  const [showSyncInfo, setShowSyncInfo] = useState(false);
  const [isSynchronizing, setIsSynchronizing] = useState(false);
  const [syncStatus, setSyncStatus] = useState<{
    synchronized: number;
    manual: number;
    total: number;
  } | null>(null);

  // Initial form values
  const initialValues: FensterFormValues = {
    fenster: defaultFenster,
  };

  // Create the form before useEffect hooks that depend on it
  const form = useForm({
    defaultValues: initialValues,
    onSubmit: async ({ value }) => {
      setSubmitError(null);
      setValidationErrors({});

      // Clear any existing field errors
      const fieldNames = Object.keys(fensterPageSchema.shape);
      clearFormFieldErrors(form, fieldNames);

      try {
        const validatedValues = fensterPageSchema.parse(value);
        saveMutation.mutate(validatedValues);
      } catch (validationError) {
        if (validationError instanceof z.ZodError) {
          // Use the improved validation error handling
          const { fieldErrors, summaryMessage } = handleZodValidationError(
            validationError,
            form,
          );
          setValidationErrors(fieldErrors);
          setSubmitError(summaryMessage);

          // Log for debugging
          console.error(
            "Form validation error:",
            validationError.flatten().fieldErrors,
          );
        } else {
          setSubmitError(
            "Ein unerwarteter Validierungsfehler ist aufgetreten.",
          );
        }
      }
    },
  });

  // Fetch certificate type to check if user should be on this page
  const { data: certificateData } = useQuery({
    queryKey: ["energieausweise", "certificate_type", activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from("energieausweise")
        .select("certificate_type")
        .eq("id", activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Redirect if certificate type is not WG/B
  useEffect(() => {
    if (certificateData && certificateData.certificate_type !== "WG/B") {
      navigate({ to: "/erfassen/objektdaten" });
    }
  }, [certificateData, navigate]);

  // Fetch walls data from gebaeudeform
  const { data: wallsData } = useQuery({
    queryKey: ["energieausweise", "gebaeudeform_walls", activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from("energieausweise")
        .select("gebaeudedetails2")
        .eq("id", activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      const gebaeudedetails2 = data?.gebaeudedetails2 as Record<
        string,
        unknown
      >;
      const waende = gebaeudedetails2?.waende;
      return Array.isArray(waende) ? waende : [];
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Fetch existing data
  const {
    data: existingData,
    isError,
    error,
  } = useQuery({
    queryKey: ["energieausweise", "fenster", activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from("energieausweise")
        .select("fenster")
        .eq("id", activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
  });

  // Show sync info when walls data is loaded and has items
  useEffect(() => {
    if (Array.isArray(wallsData) && wallsData.length > 0) {
      setShowSyncInfo(true);
    }
  }, [wallsData]);

  /**
   * Core synchronization function that matches walls with windows
   *
   * This function implements the business logic for keeping windows in sync with walls:
   * - Preserves existing window customizations (area, type, orientation, etc.)
   * - Only updates the 'bezeichnung' field to match wall names
   * - Creates new windows for new walls with sensible defaults
   * - Preserves manually added windows that don't correspond to walls
   * - Removes windows when their corresponding wall is deleted
   */
  const synchronizeWindowsWithWalls = useCallback(
    (walls: Wall[], existingWindows: Fenster[]): Fenster[] => {
      if (!walls || walls.length === 0) {
        // If no walls, return empty array as all windows are wall-synchronized
        return [];
      }

      // Create a map of existing windows by bezeichnung for quick lookup
      const existingWindowsMap = new Map<string, Fenster>();
      existingWindows.forEach((window) => {
        existingWindowsMap.set(window.bezeichnung, window);
      });

      // Create windows for each wall
      const synchronizedWindows: Fenster[] = walls.map((wall, index) => {
        const existingWindow = existingWindowsMap.get(wall.bezeichnung);

        if (existingWindow) {
          // Keep existing window but update bezeichnung if it changed
          return {
            ...existingWindow,
            bezeichnung: wall.bezeichnung,
          };
        } else {
          // Create new window for this wall
          return {
            id: `wall-sync-${wall.id || Date.now()}-${index}`,
            bezeichnung: wall.bezeichnung,
            art: "fb_KunststoffWSG",
            flaeche: "",
            uWert: "",
            ausrichtung: "0",
            baujahr: "",
          };
        }
      });

      return synchronizedWindows;
    },
    [],
  );

  // Set loading to false and synchronize windows when data is loaded
  useEffect(() => {
    // Only proceed when we have completed loading both queries
    if (existingData !== undefined && wallsData !== undefined) {
      setIsSynchronizing(true);

      if (Array.isArray(wallsData) && wallsData.length > 0) {
        // We have walls data - synchronize
        const fensterData = existingData?.fenster as Record<string, unknown>;
        const existingWindows = Array.isArray(fensterData?.fenster)
          ? (fensterData.fenster as Fenster[])
          : defaultFenster;
        const synchronizedWindows = synchronizeWindowsWithWalls(
          wallsData as Wall[],
          existingWindows,
        );

        setFenster(synchronizedWindows);
        form.setFieldValue("fenster", synchronizedWindows);
      } else {
        // No walls data, use existing windows or default
        const fensterData = existingData?.fenster as Record<string, unknown>;
        const existingWindows = Array.isArray(fensterData?.fenster)
          ? (fensterData.fenster as Fenster[])
          : defaultFenster;
        setFenster(existingWindows);
        form.setFieldValue("fenster", existingWindows);
      }

      setTimeout(() => {
        setIsSynchronizing(false);
        setIsLoading(false);

        // Update sync status
        if (Array.isArray(wallsData) && wallsData.length > 0) {
          const synchronizedCount = wallsData.length;
          setSyncStatus({
            synchronized: synchronizedCount,
            manual: 0,
            total: synchronizedCount,
          });
        } else if (!Array.isArray(wallsData) || wallsData.length === 0) {
          setSyncStatus({
            synchronized: 0,
            manual: 0,
            total: 0,
          });
        }
      }, 300); // Brief delay to show synchronization feedback
    }
  }, [
    existingData,
    wallsData,
    defaultFenster,
    form,
    synchronizeWindowsWithWalls,
    fenster.length,
  ]);

  // Watch for changes in walls data and re-synchronize
  useEffect(() => {
    if (Array.isArray(wallsData) && wallsData.length > 0 && !isLoading) {
      const synchronizedWindows = synchronizeWindowsWithWalls(
        wallsData as Wall[],
        fenster,
      );

      // Only update if there are actual changes
      const hasChanges =
        synchronizedWindows.length !== fenster.length ||
        synchronizedWindows.some(
          (window, index) =>
            !fenster[index] ||
            window.bezeichnung !== fenster[index].bezeichnung,
        );

      if (hasChanges) {
        setFenster(synchronizedWindows);
        form.setFieldValue("fenster", synchronizedWindows);
      }
    }
  }, [wallsData, isLoading, fenster, form, synchronizeWindowsWithWalls]);

  // Define the mutation for saving data to Supabase
  const saveMutation = useMutation({
    mutationFn: async (data: FensterFormValues) => {
      if (!activeCertificateId)
        throw new Error("Kein aktives Zertifikat ausgewählt.");

      const { data: result, error } = await supabase
        .from("energieausweise")
        .update({
          fenster: data,
          updated_at: new Date().toISOString(),
        })
        .eq("id", activeCertificateId)
        .select();

      if (error) throw error;
      return result;
    },
    onSuccess: async () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ["energieausweise", activeCertificateId],
      });
      queryClient.invalidateQueries({
        queryKey: ["energieausweise", "fenster", activeCertificateId],
      });

      // Update navigation state to mark next page as current
      await markPageAsVisited("heizung");

      // Navigate to the next page
      navigate({ to: "/erfassen/heizung" });
    },
    onError: (error) => {
      setSubmitError(`Fehler beim Speichern: ${error.message}`);
    },
  });

  // Fenster-Komponente für dynamische Fenster
  const FensterField = ({ index }: { index: number }) => {
    // Use Tanstack Form's useField for each field
    const bezeichnungField = useField({
      name: `fenster[${index}].bezeichnung` as const,
      form,
    });

    const artField = useField({
      name: `fenster[${index}].art` as const,
      form,
    });

    const flaecheField = useField({
      name: `fenster[${index}].flaeche` as const,
      form,
    });

    // uWertField removed from UI but uWert property maintained in schema for database compatibility

    const ausrichtungField = useField({
      name: `fenster[${index}].ausrichtung` as const,
      form,
    });

    const baujahrField = useField({
      name: `fenster[${index}].baujahr` as const,
      form,
    });

    return (
      <div className="p-4 mb-4 border rounded-md bg-blue-50 border-blue-200">
        <div className="flex justify-between items-center mb-2">
          <div className="flex items-center gap-2">
            <h4 className="font-medium">Fenster {index + 1}</h4>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              <svg
                className="w-3 h-3 mr-1"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
              Automatisch synchronisiert
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="mb-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Bezeichnung <span className="text-red-500">*</span>
              <span className="text-xs text-blue-600 ml-1">
                (automatisch von Außenwand übernommen)
              </span>
            </label>
            <input
              type="text"
              value={bezeichnungField.state.value || ""}
              onChange={(e) => bezeichnungField.handleChange(e.target.value)}
              onBlur={bezeichnungField.handleBlur}
              className="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 border-blue-300 bg-blue-50"
              placeholder="z.B. Dachflächenfenster"
              readOnly={true}
              title="Diese Bezeichnung wird automatisch mit der entsprechenden Außenwand synchronisiert"
            />
          </div>

          <div className="mb-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Art
            </label>
            <select
              value={artField.state.value || "fb_KunststoffWSG"}
              onChange={(e) => artField.handleChange(e.target.value)}
              onBlur={artField.handleBlur}
              className="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 border-gray-300"
            >
              <option value="fb_HolzEinfach">
                Holzfenster, einfach verglast
              </option>
              <option value="fb_HolzVerbund">
                Holzfenster, zweifach verglast (Kasten/Verbund)
              </option>
              <option value="fb_HolzWSG">Holzfenster, Wärmeschutzglas </option>
              <option value="fb_KunststoffWSG">
                Kunststoff mit Wärmeschutzverglasung
              </option>
              <option value="fb_Kunststoff">
                Kunststofffenster, zweifach verglast
              </option>
              <option value="fb_AluBis1983">
                Alu-/Stahlfenster, zweifach verglast - bis Bj. 83{" "}
              </option>
              <option value="fb_AluAb1984">
                Alu-/Stahlfenster, zweifach verglast - ab Bj. 84
              </option>
              <option value="fb_AluWSG">
                Alu-/Stahlfenster, Wärmeschutzglas
              </option>
              <option value="fb_Tueren">Türen</option>
              <option value="fb_Passivhaus">
                Passivhausfenster u. Türen (3-fach)
              </option>
              <option value="fb_TuerenMetall">Metalltüren</option>
            </select>
          </div>

          <div className="mb-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Fläche in m² <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={flaecheField.state.value || ""}
              onChange={(e) => flaecheField.handleChange(e.target.value)}
              onBlur={flaecheField.handleBlur}
              className="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 border-gray-300"
              placeholder="z.B. 8,1"
            />
          </div>

          {/* U-Wert field removed from UI but maintained in schema for database compatibility */}

          <div className="mb-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Ausrichtung (Grad)
            </label>
            <select
              value={ausrichtungField.state.value || "0"}
              onChange={(e) => ausrichtungField.handleChange(e.target.value)}
              onBlur={ausrichtungField.handleBlur}
              className="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 border-gray-300"
            >
              <option value="0">Nord (0°)</option>
              <option value="45">Nordost (45°)</option>
              <option value="90">Ost (90°)</option>
              <option value="135">Südost (135°)</option>
              <option value="180">Süd (180°)</option>
              <option value="225">Südwest (225°)</option>
              <option value="270">West (270°)</option>
              <option value="315">Nordwest (315°)</option>
            </select>
          </div>

          <div className="mb-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Baujahr / Jahr des letzten Austauschs
            </label>
            <input
              type="text"
              value={baujahrField.state.value || ""}
              onChange={(e) => baujahrField.handleChange(e.target.value)}
              onBlur={baujahrField.handleBlur}
              className="w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 border-gray-300"
              placeholder="z.B. 2010"
            />
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-5xl mx-auto">
      {/* Breadcrumb Navigation */}
      <Breadcrumb className="mb-6" />

      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Fenster erfassen
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Die Fenster werden automatisch basierend auf Ihren definierten
        Außenwänden erstellt. Bitte passen Sie die Fenster-Eigenschaften nach
        Bedarf an.
      </p>

      {isLoading ? (
        <div className="bg-white shadow-md rounded-lg p-6 flex justify-center items-center h-64">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-500 mb-2"></div>
            <p className="text-gray-600">Daten werden geladen...</p>
          </div>
        </div>
      ) : (
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="bg-white shadow-md rounded-lg p-6"
        >
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 pb-2 border-b">
              Fenster
            </h2>

            {/* Synchronization Info - Explains the wall-window synchronization feature */}
            {showSyncInfo &&
              Array.isArray(wallsData) &&
              wallsData.length > 0 && (
                <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 text-blue-400"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-blue-800">
                        Automatische Synchronisation mit Außenwänden
                      </h3>
                      <div className="mt-2 text-sm text-blue-700">
                        <p>
                          Die Fenster wurden automatisch basierend auf Ihren{" "}
                          {Array.isArray(wallsData) ? wallsData.length : 0}{" "}
                          definierten Außenwänden erstellt. Jedes Fenster
                          entspricht einer Außenwand und trägt denselben Namen.
                          Alle Fenster werden automatisch verwaltet.
                        </p>
                        {isSynchronizing && (
                          <div className="mt-3 flex items-center text-sm text-blue-700">
                            <svg
                              className="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                              ></circle>
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              ></path>
                            </svg>
                            Synchronisation läuft...
                          </div>
                        )}
                        <p className="mt-2">
                          <strong>Wichtige Hinweise:</strong>
                        </p>
                        <ul className="list-disc list-inside mt-1 space-y-1">
                          <li>
                            Alle Fenster werden automatisch mit den
                            entsprechenden Außenwänden synchronisiert
                          </li>
                          <li>
                            Änderungen an Wänden werden automatisch hier
                            übernommen
                          </li>
                          <li>Fensternamen sind schreibgeschützt</li>
                        </ul>
                      </div>
                      <div className="mt-3">
                        <button
                          type="button"
                          onClick={() => setShowSyncInfo(false)}
                          className="text-blue-600 hover:text-blue-500 text-sm font-medium"
                        >
                          Verstanden, ausblenden
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

            {/* Synchronization Status Summary */}
            {syncStatus && !isSynchronizing && (
              <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <div className="flex items-center text-sm text-green-800">
                  <svg
                    className="h-4 w-4 text-green-600 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span>
                    <strong>Synchronisation abgeschlossen:</strong>{" "}
                    {syncStatus.synchronized} Fenster automatisch erstellt
                  </span>
                </div>
              </div>
            )}
          </div>

          <div className="mb-8">
            {fenster.length > 0 ? (
              <>
                {fenster.map((fensterItem, index) => (
                  <FensterField key={fensterItem.id} index={index} />
                ))}
                {syncStatus && syncStatus.total > 0 && (
                  <div className="mt-2 text-sm text-gray-600">
                    <p>
                      Alle {syncStatus.synchronized} Fenster sind automatisch
                      mit Ihren Außenwänden synchronisiert.
                    </p>
                  </div>
                )}
              </>
            ) : (
              <div className="p-6 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-center">
                  <svg
                    className="h-5 w-5 text-yellow-400 mr-3"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <div>
                    <h3 className="text-sm font-medium text-yellow-800">
                      Keine Außenwände definiert
                    </h3>
                    <p className="text-sm text-yellow-700 mt-1">
                      Bitte definieren Sie zuerst Ihre Außenwände im vorherigen
                      Schritt. Fenster werden automatisch basierend auf den
                      Außenwänden erstellt.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Validation Error Summary */}
          <ValidationErrorSummary errors={validationErrors} />

          {submitError && Object.keys(validationErrors).length === 0 && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {submitError}
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8">
            <Link
              to="/erfassen/gebaeudeform"
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
            >
              Zurück
            </Link>
            <button
              type="submit"
              disabled={form.state.isSubmitting || saveMutation.isPending}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-300"
            >
              {form.state.isSubmitting || saveMutation.isPending
                ? "Wird gespeichert..."
                : "Weiter"}
            </button>
          </div>
        </form>
      )}

      {isError && (
        <div className="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          <p>
            Fehler beim Laden der Daten:{" "}
            {error instanceof Error ? error.message : "Unbekannter Fehler"}
          </p>
          <p className="mt-2">
            Bitte versuchen Sie es später erneut oder kontaktieren Sie den
            Support.
          </p>
        </div>
      )}
    </div>
  );
};
