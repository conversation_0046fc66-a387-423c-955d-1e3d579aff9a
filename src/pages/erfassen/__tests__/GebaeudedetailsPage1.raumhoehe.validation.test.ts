import { describe, it, expect } from 'vitest';

// Test the validation logic for Raum<PERSON>öhe field
describe('GebaeudedetailsPage1 Raumhöhe Validation', () => {
  // Simulate the validation function from the component
  const validateRaumhoehe = (raumhoehe: string) => {
    if (!raumhoehe || raumhoehe.trim() === '') {
      return null; // No validation if field is empty
    }

    // Convert German decimal format (comma) to English format (dot)
    const normalizedValue = raumhoehe.replace(',', '.');

    // Check if the normalized value is a valid number format
    // Only allow digits, one decimal point, and optional leading/trailing whitespace
    if (!/^\s*\d+(\.\d+)?\s*$/.test(normalizedValue)) {
      return 'Bitte geben Sie eine gültige Zahl ein';
    }

    const height = parseFloat(normalizedValue);

    if (isNaN(height)) {
      return 'Bitte geben Sie eine gültige Zahl ein';
    }

    if (height < 2.0) {
      return 'Die Raumhöhe muss zwischen 2,0 und 4,0 Metern liegen';
    }

    if (height > 4.0) {
      return 'Die Raumhöhe muss zwischen 2,0 und 4,0 Metern liegen';
    }

    return null;
  };

  describe('Valid values', () => {
    it('should accept valid room heights with dot decimal separator', () => {
      expect(validateRaumhoehe('2.0')).toBeNull();
      expect(validateRaumhoehe('2.5')).toBeNull();
      expect(validateRaumhoehe('3.0')).toBeNull();
      expect(validateRaumhoehe('3.5')).toBeNull();
      expect(validateRaumhoehe('4.0')).toBeNull();
    });

    it('should accept valid room heights with comma decimal separator (German format)', () => {
      expect(validateRaumhoehe('2,0')).toBeNull();
      expect(validateRaumhoehe('2,5')).toBeNull();
      expect(validateRaumhoehe('3,0')).toBeNull();
      expect(validateRaumhoehe('3,5')).toBeNull();
      expect(validateRaumhoehe('4,0')).toBeNull();
    });

    it('should accept integer values within range', () => {
      expect(validateRaumhoehe('2')).toBeNull();
      expect(validateRaumhoehe('3')).toBeNull();
      expect(validateRaumhoehe('4')).toBeNull();
    });

    it('should accept empty or whitespace values (no validation)', () => {
      expect(validateRaumhoehe('')).toBeNull();
      expect(validateRaumhoehe('   ')).toBeNull();
    });
  });

  describe('Invalid values - too low', () => {
    it('should reject values below 2.0 meters', () => {
      expect(validateRaumhoehe('1.9')).toBe('Die Raumhöhe muss zwischen 2,0 und 4,0 Metern liegen');
      expect(validateRaumhoehe('1,5')).toBe('Die Raumhöhe muss zwischen 2,0 und 4,0 Metern liegen');
      expect(validateRaumhoehe('1')).toBe('Die Raumhöhe muss zwischen 2,0 und 4,0 Metern liegen');
      expect(validateRaumhoehe('0.5')).toBe('Die Raumhöhe muss zwischen 2,0 und 4,0 Metern liegen');
    });
  });

  describe('Invalid values - too high', () => {
    it('should reject values above 4.0 meters', () => {
      expect(validateRaumhoehe('4.1')).toBe('Die Raumhöhe muss zwischen 2,0 und 4,0 Metern liegen');
      expect(validateRaumhoehe('5,0')).toBe('Die Raumhöhe muss zwischen 2,0 und 4,0 Metern liegen');
      expect(validateRaumhoehe('25')).toBe('Die Raumhöhe muss zwischen 2,0 und 4,0 Metern liegen');
      expect(validateRaumhoehe('10.5')).toBe('Die Raumhöhe muss zwischen 2,0 und 4,0 Metern liegen');
    });
  });

  describe('Invalid values - non-numeric', () => {
    it('should reject non-numeric values', () => {
      expect(validateRaumhoehe('abc')).toBe('Bitte geben Sie eine gültige Zahl ein');
      expect(validateRaumhoehe('2.5m')).toBe('Bitte geben Sie eine gültige Zahl ein');
      expect(validateRaumhoehe('zwei')).toBe('Bitte geben Sie eine gültige Zahl ein');
      expect(validateRaumhoehe('2..5')).toBe('Bitte geben Sie eine gültige Zahl ein');
      expect(validateRaumhoehe('2,5,0')).toBe('Bitte geben Sie eine gültige Zahl ein');
    });
  });

  describe('Edge cases', () => {
    it('should handle exact boundary values', () => {
      expect(validateRaumhoehe('2.0')).toBeNull();
      expect(validateRaumhoehe('4.0')).toBeNull();
      expect(validateRaumhoehe('1.999')).toBe('Die Raumhöhe muss zwischen 2,0 und 4,0 Metern liegen');
      expect(validateRaumhoehe('4.001')).toBe('Die Raumhöhe muss zwischen 2,0 und 4,0 Metern liegen');
    });

    it('should handle very precise decimal values', () => {
      expect(validateRaumhoehe('2.75')).toBeNull();
      expect(validateRaumhoehe('3.14159')).toBeNull();
      expect(validateRaumhoehe('2,333')).toBeNull();
    });
  });
});
