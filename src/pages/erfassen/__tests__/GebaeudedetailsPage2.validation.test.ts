import { z } from 'zod';

// Import the schemas from the component
const bauteilSchema = z.object({
  id: z.string(),
  bezeichnung: z.string().min(1, 'Bezeichnung ist erforderlich'),
  massiv: z.string().optional(),
  uebergang: z.string().optional(),
  flaeche: z.string().min(1, 'Fläche ist erforderlich'),
  uWert: z.string().optional(),
});

const gebaeudedetails2Schema = z.object({
  // Gebäudedetails Teil 2
  kuehlWfl: z.string().default('0'),
  Originaldaemmstandard: z.enum(['0', '1', '2']).default('0'),
  bjFensterAustausch: z.string().optional(),
  Fensterlüftung: z.enum(['0', '1']).default('0'),
  Schachtlüftung: z.enum(['0', '1']).default('0'),
  L_Mit_WRG: z.enum(['0', '1']).default('0'),
  L_Ohne_WRG: z.enum(['0', '1']).default('0'),

  // Moved from GebaeudedetailsPage1
  Keller_beheizt: z.enum(['0', '1']).default('0').optional(),
  Klimatisiert: z.enum(['0', '1']).default('0'),

  // Erneuerbare Energien Felder (von TwwLueftungPage verschoben)
  TW_Solar: z.enum(['0', '1']).default('0'),
  HZ_Solar: z.enum(['0', '1']).default('0'),
  TW_WP: z.enum(['0', '1']).default('0'),
  HZ_WP: z.enum(['0', '1']).default('0'),

  // Dämmungsfelder für alle Zertifikatstypen
  Boden1_Dämmung: z.string().default('0'),
  Dach1_Dämmung: z.string().default('0'),
  Wand1_Dämmung: z.string().default('0'),

  // Bauteile (dynamisch)
  boeden: z.array(bauteilSchema).default([]),
  daecher: z.array(bauteilSchema).default([]),
  waende: z.array(bauteilSchema).default([]),
});

describe('GebaeudedetailsPage2 Validation', () => {
  const baseValidData = {
    kuehlWfl: '0',
    Originaldaemmstandard: '0' as const,
    Fensterlüftung: '0' as const,
    Schachtlüftung: '0' as const,
    L_Mit_WRG: '0' as const,
    L_Ohne_WRG: '0' as const,
    Klimatisiert: '0' as const,
    TW_Solar: '0' as const,
    HZ_Solar: '0' as const,
    TW_WP: '0' as const,
    HZ_WP: '0' as const,
    Boden1_Dämmung: '0',
    Dach1_Dämmung: '0',
    Wand1_Dämmung: '0',
    boeden: [],
    daecher: [],
    waende: [],
  };

  describe('Building component validation for WG/B certificate type', () => {
    test('should fail validation when flaeche is empty in waende for WG/B', () => {
      const testData = {
        ...baseValidData,
        waende: [
          {
            id: 'test-1',
            bezeichnung: 'Test Wand',
            massiv: 'kb_Massiv',
            uebergang: '',
            flaeche: '', // Empty flaeche - should cause validation error
            uWert: ''
          }
        ]
      };

      const result = gebaeudedetails2Schema.safeParse(testData);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        const fieldErrors = result.error.flatten().fieldErrors;
        expect(fieldErrors.waende).toBeDefined();
        expect(fieldErrors.waende?.[0]).toContain('Fläche ist erforderlich');
      }
    });

    test('should fail validation when flaeche is empty in boeden for WG/B', () => {
      const testData = {
        ...baseValidData,
        boeden: [
          {
            id: 'test-1',
            bezeichnung: 'Test Boden',
            massiv: 'kb_Massiv',
            uebergang: '1',
            flaeche: '', // Empty flaeche - should cause validation error
            uWert: ''
          }
        ]
      };

      const result = gebaeudedetails2Schema.safeParse(testData);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        const fieldErrors = result.error.flatten().fieldErrors;
        expect(fieldErrors.boeden).toBeDefined();
        expect(fieldErrors.boeden?.[0]).toContain('Fläche ist erforderlich');
      }
    });

    test('should fail validation when flaeche is empty in daecher for WG/B', () => {
      const testData = {
        ...baseValidData,
        daecher: [
          {
            id: 'test-1',
            bezeichnung: 'Test Dach',
            massiv: '0',
            uebergang: '0',
            flaeche: '', // Empty flaeche - should cause validation error
            uWert: ''
          }
        ]
      };

      const result = gebaeudedetails2Schema.safeParse(testData);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        const fieldErrors = result.error.flatten().fieldErrors;
        expect(fieldErrors.daecher).toBeDefined();
        expect(fieldErrors.daecher?.[0]).toContain('Fläche ist erforderlich');
      }
    });

    test('should pass validation when all flaeche fields are filled', () => {
      const testData = {
        ...baseValidData,
        waende: [
          {
            id: 'test-1',
            bezeichnung: 'Test Wand',
            massiv: 'kb_Massiv',
            uebergang: '',
            flaeche: '100', // Valid flaeche
            uWert: ''
          }
        ],
        boeden: [
          {
            id: 'test-2',
            bezeichnung: 'Test Boden',
            massiv: 'kb_Massiv',
            uebergang: '1',
            flaeche: '50', // Valid flaeche
            uWert: ''
          }
        ],
        daecher: [
          {
            id: 'test-3',
            bezeichnung: 'Test Dach',
            massiv: '0',
            uebergang: '0',
            flaeche: '75', // Valid flaeche
            uWert: ''
          }
        ]
      };

      const result = gebaeudedetails2Schema.safeParse(testData);
      expect(result.success).toBe(true);
    });
  });

  describe('Non-WG/B certificate type validation', () => {
    test('should pass validation with empty building component arrays', () => {
      const testData = {
        ...baseValidData,
        // Empty arrays for non-WG/B types
        boeden: [],
        daecher: [],
        waende: [],
      };

      const result = gebaeudedetails2Schema.safeParse(testData);
      expect(result.success).toBe(true);
    });
  });
});
