import { z } from 'zod';

// Test the validation schema for consumption amount fields
describe('VerbrauchPage Required Fields Validation', () => {
  // Define the same schema as in the component
  const verbrauchSchema = z.object({
    // Energieträger 1 (ETr1)
    ETr1_Kategorie: z.string().optional(),
    ETr1_Heizung: z.enum(['0', '1']).default('1'),
    ETr1_TWW: z.enum(['0', '1']).default('1'),
    ETr1_ZusatzHz: z.enum(['0', '1']).default('0'),
    ETr1_Lueften: z.enum(['0', '1']).default('0'),
    ETr1_Licht: z.enum(['0', '1']).default('0'),
    ETr1_Kuehlen: z.enum(['0', '1']).default('0'),
    ETr1_Sonst: z.enum(['0', '1']).default('0'),
    ETr1_PrimFaktor: z.string().default('1'),
    ETr1_Anteil_erneuerbar: z.string().optional(),
    ETr1_Anteil_KWK: z.string().optional(),
    ETr1_isFw: z.enum(['0', '1']).default('0'),
    ETr1_gebaeudeNahErzeugt: z.enum(['0', '1']).default('0'),
    ETr1_Name: z.string().optional(),

    // Jahr 1 für ETr1 - consumption amounts are required
    ETr1_Jahr1_von: z.string().optional(),
    ETr1_Jahr1_bis: z.string().optional(),
    ETr1_Jahr1_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
    ETr1_Jahr1_Leerstand: z.string().optional(),
    ETr1_Jahr1_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
    ETr1_Jahr1_Leerstand_von: z.string().optional(),
    ETr1_Jahr1_Leerstand_bis: z.string().optional(),

    // Jahr 2 für ETr1 - consumption amounts are required
    ETr1_Jahr2_von: z.string().optional(),
    ETr1_Jahr2_bis: z.string().optional(),
    ETr1_Jahr2_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
    ETr1_Jahr2_Leerstand: z.string().optional(),
    ETr1_Jahr2_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
    ETr1_Jahr2_Leerstand_von: z.string().optional(),
    ETr1_Jahr2_Leerstand_bis: z.string().optional(),

    // Jahr 3 für ETr1 - consumption amounts are required
    ETr1_Jahr3_von: z.string().optional(),
    ETr1_Jahr3_bis: z.string().optional(),
    ETr1_Jahr3_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
    ETr1_Jahr3_Leerstand: z.string().optional(),
    ETr1_Jahr3_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
    ETr1_Jahr3_Leerstand_von: z.string().optional(),
    ETr1_Jahr3_Leerstand_bis: z.string().optional(),

    // Energieträger 2 (ETr2) - consumption amounts are required
    ETr2_Kategorie: z.string().optional(),
    ETr2_Heizung: z.enum(['0', '1']).default('0'),
    ETr2_TWW: z.enum(['0', '1']).default('0'),
    ETr2_ZusatzHz: z.enum(['0', '1']).default('0'),
    ETr2_Lueften: z.enum(['0', '1']).default('0'),
    ETr2_Licht: z.enum(['0', '1']).default('0'),
    ETr2_Kuehlen: z.enum(['0', '1']).default('0'),
    ETr2_Sonst: z.enum(['0', '1']).default('0'),
    ETr2_PrimFaktor: z.string().default('1'),
    ETr2_Anteil_erneuerbar: z.string().optional(),
    ETr2_Anteil_KWK: z.string().optional(),
    ETr2_isFw: z.enum(['0', '1']).default('0'),
    ETr2_gebaeudeNahErzeugt: z.enum(['0', '1']).default('0'),
    ETr2_Name: z.string().optional(),

    ETr2_Jahr1_von: z.string().optional(),
    ETr2_Jahr1_bis: z.string().optional(),
    ETr2_Jahr1_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
    ETr2_Jahr1_Leerstand: z.string().optional(),
    ETr2_Jahr1_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
    ETr2_Jahr1_Leerstand_von: z.string().optional(),
    ETr2_Jahr1_Leerstand_bis: z.string().optional(),

    ETr2_Jahr2_von: z.string().optional(),
    ETr2_Jahr2_bis: z.string().optional(),
    ETr2_Jahr2_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
    ETr2_Jahr2_Leerstand: z.string().optional(),
    ETr2_Jahr2_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
    ETr2_Jahr2_Leerstand_von: z.string().optional(),
    ETr2_Jahr2_Leerstand_bis: z.string().optional(),

    ETr2_Jahr3_von: z.string().optional(),
    ETr2_Jahr3_bis: z.string().optional(),
    ETr2_Jahr3_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
    ETr2_Jahr3_Leerstand: z.string().optional(),
    ETr2_Jahr3_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
    ETr2_Jahr3_Leerstand_von: z.string().optional(),
    ETr2_Jahr3_Leerstand_bis: z.string().optional(),

    // Energieträger 3 (ETr3) - consumption amounts are required
    ETr3_Kategorie: z.string().optional(),
    ETr3_Heizung: z.enum(['0', '1']).default('0'),
    ETr3_TWW: z.enum(['0', '1']).default('0'),
    ETr3_ZusatzHz: z.enum(['0', '1']).default('0'),
    ETr3_Lueften: z.enum(['0', '1']).default('0'),
    ETr3_Licht: z.enum(['0', '1']).default('0'),
    ETr3_Kuehlen: z.enum(['0', '1']).default('0'),
    ETr3_Sonst: z.enum(['0', '1']).default('0'),
    ETr3_PrimFaktor: z.string().default('1'),
    ETr3_Anteil_erneuerbar: z.string().optional(),
    ETr3_Anteil_KWK: z.string().optional(),
    ETr3_isFw: z.enum(['0', '1']).default('0'),
    ETr3_gebaeudeNahErzeugt: z.enum(['0', '1']).default('0'),
    ETr3_Name: z.string().optional(),

    ETr3_Jahr1_von: z.string().optional(),
    ETr3_Jahr1_bis: z.string().optional(),
    ETr3_Jahr1_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
    ETr3_Jahr1_Leerstand: z.string().optional(),
    ETr3_Jahr1_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
    ETr3_Jahr1_Leerstand_von: z.string().optional(),
    ETr3_Jahr1_Leerstand_bis: z.string().optional(),

    ETr3_Jahr2_von: z.string().optional(),
    ETr3_Jahr2_bis: z.string().optional(),
    ETr3_Jahr2_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
    ETr3_Jahr2_Leerstand: z.string().optional(),
    ETr3_Jahr2_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
    ETr3_Jahr2_Leerstand_von: z.string().optional(),
    ETr3_Jahr2_Leerstand_bis: z.string().optional(),

    ETr3_Jahr3_von: z.string().optional(),
    ETr3_Jahr3_bis: z.string().optional(),
    ETr3_Jahr3_Menge: z.string().min(1, 'Verbrauchsmenge ist erforderlich'),
    ETr3_Jahr3_Leerstand: z.string().optional(),
    ETr3_Jahr3_Leerstand_hasLeerstand: z.enum(['0', '1']).default('0'),
    ETr3_Jahr3_Leerstand_von: z.string().optional(),
    ETr3_Jahr3_Leerstand_bis: z.string().optional(),
  });

  describe('Consumption Amount Fields Validation', () => {
    it('should require ETr1_Jahr1_Menge field', () => {
      const invalidData = {
        ETr1_Jahr1_Menge: '', // Empty string should fail validation
      };

      const result = verbrauchSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        const mengeError = result.error.errors.find(err => 
          err.path.includes('ETr1_Jahr1_Menge')
        );
        expect(mengeError).toBeDefined();
        expect(mengeError?.message).toBe('Verbrauchsmenge ist erforderlich');
      }
    });

    it('should require ETr1_Jahr2_Menge field', () => {
      const invalidData = {
        ETr1_Jahr2_Menge: '', // Empty string should fail validation
      };

      const result = verbrauchSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        const mengeError = result.error.errors.find(err => 
          err.path.includes('ETr1_Jahr2_Menge')
        );
        expect(mengeError).toBeDefined();
        expect(mengeError?.message).toBe('Verbrauchsmenge ist erforderlich');
      }
    });

    it('should require ETr1_Jahr3_Menge field', () => {
      const invalidData = {
        ETr1_Jahr3_Menge: '', // Empty string should fail validation
      };

      const result = verbrauchSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        const mengeError = result.error.errors.find(err => 
          err.path.includes('ETr1_Jahr3_Menge')
        );
        expect(mengeError).toBeDefined();
        expect(mengeError?.message).toBe('Verbrauchsmenge ist erforderlich');
      }
    });

    it('should accept valid consumption amount values', () => {
      const validData = {
        // Provide all required consumption amount fields with valid values
        ETr1_Jahr1_Menge: '15000',
        ETr1_Jahr2_Menge: '14500',
        ETr1_Jahr3_Menge: '16000',
        ETr2_Jahr1_Menge: '12000',
        ETr2_Jahr2_Menge: '11500',
        ETr2_Jahr3_Menge: '13000',
        ETr3_Jahr1_Menge: '8000',
        ETr3_Jahr2_Menge: '7500',
        ETr3_Jahr3_Menge: '9000',
      };

      const result = verbrauchSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should require all ETr2 consumption amount fields', () => {
      const invalidData = {
        ETr2_Jahr1_Menge: '',
        ETr2_Jahr2_Menge: '',
        ETr2_Jahr3_Menge: '',
      };

      const result = verbrauchSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        const mengeErrors = result.error.errors.filter(err => 
          err.path.some(path => typeof path === 'string' && path.includes('ETr2') && path.includes('Menge'))
        );
        expect(mengeErrors).toHaveLength(3);
        mengeErrors.forEach(error => {
          expect(error.message).toBe('Verbrauchsmenge ist erforderlich');
        });
      }
    });

    it('should require all ETr3 consumption amount fields', () => {
      const invalidData = {
        ETr3_Jahr1_Menge: '',
        ETr3_Jahr2_Menge: '',
        ETr3_Jahr3_Menge: '',
      };

      const result = verbrauchSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      
      if (!result.success) {
        const mengeErrors = result.error.errors.filter(err => 
          err.path.some(path => typeof path === 'string' && path.includes('ETr3') && path.includes('Menge'))
        );
        expect(mengeErrors).toHaveLength(3);
        mengeErrors.forEach(error => {
          expect(error.message).toBe('Verbrauchsmenge ist erforderlich');
        });
      }
    });
  });
});
