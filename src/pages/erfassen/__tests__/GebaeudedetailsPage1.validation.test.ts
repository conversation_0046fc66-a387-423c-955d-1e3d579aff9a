import { z } from 'zod';

// Test the validation schema for anbauSituation field
describe('GebaeudedetailsPage1 anbauSituation Validation', () => {
  // Define the same schema as in the component
  const gebaeudedetailsSchema = z.object({
    BedarfVerbrauch: z.enum(['V', 'B']).default('V'),
    Anlass: z.enum(['AG_VERMIETUNG', 'AG_AUSHANG', 'AG_SONST']).default('AG_VERMIETUNG'),
    Datenerhebung: z.enum(['0', '1']).default('0'),
    nichtWohnGeb: z.enum(['0', '1']).default('0'),
    isGebaeudehuelle: z.enum(['0', '1']).default('1'),
    Nutzung1_ID: z.string().optional(),
    Nutzung1_Flaeche: z.string().optional(),
    Baujahr: z.string().min(4, 'Baujahr ist erforderlich'),
    Modernisierung: z.string().optional(),
    Wohnfläche: z.string().min(1, 'Wohnfläche ist erforderlich'),
    Raumhöhe: z.string().optional(),
    Volumen: z.string().optional(),
    Wohneinheiten: z.string().min(1, 'Anzahl der Wohneinheiten ist erforderlich'),
    Geschosse: z.string().optional(),
    anbauSituation: z.enum(['0', '1', '2']).optional().default('0'),
    ergaenzendeErlaeuterungen: z.string().optional(),
    baujahrHzErz: z.string().min(1, 'Baujahr Heizung ist erforderlich'),
  });

  type GebaeudedetailsFormValues = z.infer<typeof gebaeudedetailsSchema>;

  // Mock validation function that mimics the component's validation logic
  const validateAnbauSituation = (
    values: GebaeudedetailsFormValues,
    certificateType: 'WG/V' | 'WG/B' | 'NWG/V'
  ): string | null => {
    if (certificateType === 'WG/B') {
      if (values.anbauSituation === undefined || values.anbauSituation === null) {
        return 'Anbausituation ist für Bedarfsausweise erforderlich';
      }
    }
    return null;
  };

  describe('anbauSituation field validation', () => {
    const baseValidData: GebaeudedetailsFormValues = {
      BedarfVerbrauch: 'B',
      Anlass: 'AG_VERMIETUNG',
      Datenerhebung: '0',
      nichtWohnGeb: '0',
      isGebaeudehuelle: '1',
      Baujahr: '1975',
      Wohnfläche: '100',
      Wohneinheiten: '1',
      baujahrHzErz: '1980',
      anbauSituation: '0', // Default value
    };

    test('should accept "0" (Freistehend) as valid value for WG/B certificate', () => {
      const testData = { ...baseValidData, anbauSituation: '0' as const };
      
      // Schema validation should pass
      const result = gebaeudedetailsSchema.safeParse(testData);
      expect(result.success).toBe(true);
      
      if (result.success) {
        // Custom validation should pass
        const validationError = validateAnbauSituation(result.data, 'WG/B');
        expect(validationError).toBeNull();
      }
    });

    test('should accept "1" (Einseitig angebaut) as valid value for WG/B certificate', () => {
      const testData = { ...baseValidData, anbauSituation: '1' as const };
      
      const result = gebaeudedetailsSchema.safeParse(testData);
      expect(result.success).toBe(true);
      
      if (result.success) {
        const validationError = validateAnbauSituation(result.data, 'WG/B');
        expect(validationError).toBeNull();
      }
    });

    test('should accept "2" (Zweiseitig angebaut) as valid value for WG/B certificate', () => {
      const testData = { ...baseValidData, anbauSituation: '2' as const };
      
      const result = gebaeudedetailsSchema.safeParse(testData);
      expect(result.success).toBe(true);
      
      if (result.success) {
        const validationError = validateAnbauSituation(result.data, 'WG/B');
        expect(validationError).toBeNull();
      }
    });

    test('should fail validation when anbauSituation is undefined for WG/B certificate', () => {
      const testData = { ...baseValidData };
      delete (testData as any).anbauSituation;
      
      const result = gebaeudedetailsSchema.safeParse(testData);
      expect(result.success).toBe(true); // Schema should still pass due to default value
      
      if (result.success) {
        // But if we manually set it to undefined, custom validation should fail
        const modifiedData = { ...result.data, anbauSituation: undefined as any };
        const validationError = validateAnbauSituation(modifiedData, 'WG/B');
        expect(validationError).toBe('Anbausituation ist für Bedarfsausweise erforderlich');
      }
    });

    test('should not require anbauSituation for non-WG/B certificate types', () => {
      const testData = { ...baseValidData };
      delete (testData as any).anbauSituation;
      
      const result = gebaeudedetailsSchema.safeParse(testData);
      expect(result.success).toBe(true);
      
      if (result.success) {
        // Should not fail validation for WG/V certificate type
        const validationError = validateAnbauSituation(result.data, 'WG/V');
        expect(validationError).toBeNull();
        
        // Should not fail validation for NWG/V certificate type
        const validationErrorNWG = validateAnbauSituation(result.data, 'NWG/V');
        expect(validationErrorNWG).toBeNull();
      }
    });

    test('should use default value "0" when field is not provided', () => {
      const testData = { ...baseValidData };
      delete (testData as any).anbauSituation;
      
      const result = gebaeudedetailsSchema.safeParse(testData);
      expect(result.success).toBe(true);
      
      if (result.success) {
        expect(result.data.anbauSituation).toBe('0');
      }
    });
  });

  describe('Schema validation edge cases', () => {
    test('should reject invalid enum values', () => {
      const testData = {
        BedarfVerbrauch: 'B',
        Anlass: 'AG_VERMIETUNG',
        Datenerhebung: '0',
        nichtWohnGeb: '0',
        isGebaeudehuelle: '1',
        Baujahr: '1975',
        Wohnfläche: '100',
        Wohneinheiten: '1',
        baujahrHzErz: '1980',
        anbauSituation: '3', // Invalid value
      };
      
      const result = gebaeudedetailsSchema.safeParse(testData);
      expect(result.success).toBe(false);
    });
  });
});
