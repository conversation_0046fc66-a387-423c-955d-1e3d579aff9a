/**
 * Test file for HomePage pricing functionality
 * Tests the quiz result section pricing display
 */

import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { HomePage } from '../HomePage';
import { PricingService } from '../../services/pricingService';

// Mock the PricingService
vi.mock('../../services/pricingService', () => ({
  PricingService: {
    getPricingDisplayInfo: vi.fn(),
    getPricingDisplayInfoForType: vi.fn(),
    formatPriceEuros: vi.fn((cents: number) => {
      const euros = cents / 100;
      return `${euros.toFixed(2).replace('.', ',')} €`;
    })
  }
}));

// Mock other dependencies
vi.mock('../../contexts/AuthContext', () => ({
  useAuth: () => ({ user: null })
}));

vi.mock('../../hooks/useAnonymousAuth', () => ({
  useAnonymousAuth: () => ({
    createCertificateWithAuth: vi.fn()
  })
}));

vi.mock('@tanstack/react-router', () => ({
  Link: ({ children, ...props }: any) => <a {...props}>{children}</a>,
  useNavigate: () => vi.fn()
}));

// Mock pricing data
const mockPricingData = [
  {
    certificate_type: 'WG/V' as const,
    price_euros: '49,00 €',
    price_cents: 4900,
    display_name: 'Wohngebäude-Verbrauchsausweis',
    description: 'Für Wohngebäude basierend auf dem tatsächlichen Energieverbrauch',
    stripe_price_id: 'price_test_wgv'
  },
  {
    certificate_type: 'WG/B' as const,
    price_euros: '49,00 €',
    price_cents: 4900,
    display_name: 'Wohngebäude-Bedarfsausweis',
    description: 'Für Wohngebäude basierend auf dem berechneten Energiebedarf',
    stripe_price_id: 'price_test_wgb'
  },
  {
    certificate_type: 'NWG/V' as const,
    price_euros: '59,00 €',
    price_cents: 5900,
    display_name: 'Nicht-Wohngebäude-Verbrauchsausweis',
    description: 'Für Nicht-Wohngebäude basierend auf dem tatsächlichen Energieverbrauch',
    stripe_price_id: 'price_test_nwgv'
  }
];

describe('HomePage Pricing Display', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    vi.clearAllMocks();
  });

  const renderWithQueryClient = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    );
  };

  it('should display pricing information in quiz result for WG/V certificate', async () => {
    // Mock successful pricing fetch
    vi.mocked(PricingService.getPricingDisplayInfo).mockResolvedValue(mockPricingData);

    renderWithQueryClient(<HomePage />);

    // Wait for the component to load
    await waitFor(() => {
      expect(screen.getByText('Willkommen bei der Energieausweis App')).toBeInTheDocument();
    });

    // The pricing should be fetched when the quiz component loads
    expect(PricingService.getPricingDisplayInfo).toHaveBeenCalled();
  });

  it('should show fallback price when pricing service fails', async () => {
    // Mock failed pricing fetch
    vi.mocked(PricingService.getPricingDisplayInfo).mockRejectedValue(new Error('Network error'));

    renderWithQueryClient(<HomePage />);

    await waitFor(() => {
      expect(screen.getByText('Willkommen bei der Energieausweis App')).toBeInTheDocument();
    });

    // Should still attempt to fetch pricing
    expect(PricingService.getPricingDisplayInfo).toHaveBeenCalled();
  });

  it('should format prices correctly using German format', () => {
    const formatPrice = PricingService.formatPriceEuros;
    
    expect(formatPrice(4900)).toBe('49,00 €');
    expect(formatPrice(5900)).toBe('59,00 €');
    expect(formatPrice(1000)).toBe('10,00 €');
  });

  it('should handle different certificate types with correct pricing', () => {
    const wgvPricing = mockPricingData.find(p => p.certificate_type === 'WG/V');
    const wgbPricing = mockPricingData.find(p => p.certificate_type === 'WG/B');
    const nwgvPricing = mockPricingData.find(p => p.certificate_type === 'NWG/V');

    expect(wgvPricing?.price_euros).toBe('49,00 €');
    expect(wgbPricing?.price_euros).toBe('49,00 €');
    expect(nwgvPricing?.price_euros).toBe('59,00 €');
  });
});
