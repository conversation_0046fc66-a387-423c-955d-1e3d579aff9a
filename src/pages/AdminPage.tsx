
import { useQuery } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import { LoadingSpinner, ErrorMessage } from '../components/ui/StatusMessages';
import { CsvExportButton } from '../components/admin/CsvExportButton';
import { PricingManagement } from '../components/admin/PricingManagement';
import { ExpandableTableRow } from '../components/admin/ExpandableTableRow';
import { WebhookHealthMonitor } from '../components/admin/WebhookHealthMonitor';

// Import the consolidated certificate status type
import type { CertificateStatus } from '../types/csv';

// Define certificate type
type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

interface Energieausweis {
  id: string;
  updated_at: string | null;
  certificate_type: CertificateType | null;
  status: CertificateStatus;
  order_number: string | null;
  user_id: string | null;
  admin_loaded_at?: string | null; // When CSV was last downloaded (optional until migration)
  admin_loaded?: string | null; // Email of admin who last downloaded (optional until migration)
}

interface PaymentAnalytics {
  total_certificates: number;
  paid_certificates: number;
  failed_certificates: number;
  expired_certificates: number;
  disputed_certificates: number;
  unpaid_certificates: number;
  total_revenue_cents: number;
  conversion_rate: number;
}

interface WebhookEventSummary {
  event_type: string;
  processing_status: string;
  count: number;
  latest_event: string;
}

interface PaymentAttemptSummary {
  attempt_status: string;
  count: number;
  avg_session_duration: number;
  latest_attempt: string;
}


// Helper function to get status display name using consolidated status
const getStatusName = (status: CertificateStatus | null): string => {
  // Handle consolidated status values
  if (status === 'payment_complete') {
    return 'Bezahlt';
  }
  if (status === 'payment_expired') {
    return 'Zahlung abgelaufen';
  }
  if (status === 'payment_failed') {
    return 'Zahlung fehlgeschlagen';
  }
  if (status === 'payment_disputed') {
    return 'Zahlung bestritten';
  }
  if (status === 'payment_initiated') {
    return 'Zahlung eingeleitet';
  }


  // Certificate completion status based on page-based status system
  switch (status) {
    case 'zusammenfassung':
      return 'Bereit zur Zahlung';
    case 'verbrauch':
      return 'In Bearbeitung (Verbrauchsdaten)';
    case 'tww-lueftung':
      return 'In Bearbeitung (Trinkwarmwasser/Lüftung)';
    case 'heizung':
      return 'In Bearbeitung (Heizung)';
    case 'fenster':
      return 'In Bearbeitung (Fenster)';
    case 'gebaeudedetails2':
      return 'In Bearbeitung (Gebäudedetails)';
    case 'gebaeudedetails1':
      return 'In Bearbeitung (Gebäudedetails)';
    case 'objektdaten':
      return 'In Bearbeitung (Objektdaten)';
    default:
      return 'Unbekannt';
  }
};

export const AdminPage = () => {

  // Query for certificates with simplified download tracking
  const { data: energieausweise, isLoading, error } = useQuery<Energieausweis[]>({
    queryKey: ['adminEnergieausweise'],
    queryFn: async () => {
      // Try to select with new columns first, fallback to basic columns if migration not applied
      let { data, error } = await supabase
        .from('energieausweise')
        .select('id, updated_at, certificate_type, status, order_number, user_id, admin_loaded_at, admin_loaded')
        .order('updated_at', { ascending: false });

      // If error due to missing columns, fallback to basic query
      if (error && error.message.includes('admin_loaded')) {
        console.log('New columns not yet available, using basic query');
        const fallbackResult = await supabase
          .from('energieausweise')
          .select('id, updated_at, certificate_type, status, order_number, user_id')
          .order('updated_at', { ascending: false });

        // Add missing properties to match interface
        data = fallbackResult.data?.map(item => ({
          ...item,
          admin_loaded_at: null,
          admin_loaded: null
        })) || null;
        error = fallbackResult.error;
      }

      if (error) throw error;
      return data as Energieausweis[] || [];
    },
    refetchInterval: 30000, // Refresh every 30 seconds to keep download status current
  });

  // Payment analytics query
  const { data: paymentAnalytics, isLoading: analyticsLoading } = useQuery<PaymentAnalytics>({
    queryKey: ['paymentAnalytics'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_payment_analytics');
      if (error) throw error;
      return data as unknown as PaymentAnalytics;
    },
  });



  // Webhook events summary
  const { data: webhookEvents, isLoading: webhookLoading } = useQuery<WebhookEventSummary[]>({
    queryKey: ['webhookEventsSummary'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('stripe_webhook_events')
        .select('event_type, processing_status, created_at')
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Group by event_type and processing_status
      const grouped = data.reduce((acc: Record<string, WebhookEventSummary>, event) => {
        const key = `${event.event_type}-${event.processing_status}`;
        if (!acc[key]) {
          acc[key] = {
            event_type: event.event_type,
            processing_status: event.processing_status,
            count: 0,
            latest_event: event.created_at || new Date().toISOString()
          };
        }
        acc[key].count++;
        if (event.created_at && event.created_at > acc[key].latest_event) {
          acc[key].latest_event = event.created_at;
        }
        return acc;
      }, {});

      return Object.values(grouped);
    },
  });

  // Payment attempts summary
  const { data: paymentAttempts, isLoading: attemptsLoading } = useQuery<PaymentAttemptSummary[]>({
    queryKey: ['paymentAttemptsSummary'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('payment_attempts')
        .select('attempt_status, session_duration_seconds, created_at')
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Group by attempt_status
      const grouped = data.reduce((acc: Record<string, PaymentAttemptSummary>, attempt) => {
        const key = attempt.attempt_status;
        if (!acc[key]) {
          acc[key] = {
            attempt_status: attempt.attempt_status,
            count: 0,
            avg_session_duration: 0,
            latest_attempt: attempt.created_at || new Date().toISOString()
          };
        }
        acc[key].count++;
        if (attempt.session_duration_seconds) {
          acc[key].avg_session_duration =
            (acc[key].avg_session_duration * (acc[key].count - 1) + attempt.session_duration_seconds) / acc[key].count;
        }
        if (attempt.created_at && attempt.created_at > acc[key].latest_attempt) {
          acc[key].latest_attempt = attempt.created_at;
        }
        return acc;
      }, {});

      return Object.values(grouped);
    },
  });



  const formatCurrency = (cents: number) => {
    return new Intl.NumberFormat('de-DE', {
      style: 'currency',
      currency: 'EUR'
    }).format(cents / 100);
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto py-8">
        <LoadingSpinner message="Lade Energieausweise..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-6xl mx-auto py-8">
        <ErrorMessage message={`Fehler beim Laden der Energieausweise: ${error.message}`} />
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Admin-Dashboard</h1>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Energieausweise</h2>
      </div>
      {/* Certificates Content */}
      {energieausweise && energieausweise.length > 0 ? (
        <div className="overflow-x-auto shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
          <table className="min-w-full divide-y divide-gray-300">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="w-12 px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  {/* Expand/Collapse column */}
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Bestellnummer
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Geändert Am
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Zertifikatstyp
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Status
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Download Status
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                  Aktionen
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {energieausweise.map((ea) => (
                <ExpandableTableRow key={ea.id} certificate={ea}>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {ea.order_number || 'N/A'}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {ea.updated_at ? new Date(ea.updated_at).toLocaleDateString('de-DE') : 'N/A'}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {ea.certificate_type}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                      ${ea.status === 'payment_complete'
                        ? 'bg-green-100 text-green-800'
                        : ea.status === 'payment_failed' || ea.status === 'payment_expired' || ea.status === 'payment_disputed'
                          ? 'bg-red-100 text-red-800'
                          : ea.status === 'zusammenfassung' || ea.status === 'payment_initiated'
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-yellow-100 text-yellow-800'}`}>
                      {getStatusName(ea.status)}
                    </span>
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    {ea.admin_loaded_at ? (
                      <div className="text-xs">
                        <div className="text-green-600 font-medium">
                          {new Date(ea.admin_loaded_at).toLocaleDateString('de-DE')}
                        </div>
                        <div className="text-gray-500">
                          {ea.admin_loaded || 'Unbekannt'}
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-400">Noch nicht heruntergeladen</span>
                    )}
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                    <CsvExportButton
                      certificateId={ea.id}
                      certificateType={ea.certificate_type}
                      orderNumber={ea.order_number}
                      className="text-xs"
                    />
                  </td>
                </ExpandableTableRow>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="border border-gray-200 rounded-md p-4 text-center text-gray-500">
          Keine Energieausweise gefunden.
        </div>
      )}

      {/* Payment Analytics Section */}
      <div className="my-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Zahlungsanalyse</h2>
        {analyticsLoading ? (
          <LoadingSpinner message="Lade Zahlungsanalyse..." />
        ) : paymentAnalytics ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Gesamt Zertifikate</h3>
              <p className="text-2xl font-bold text-gray-900">{paymentAnalytics.total_certificates}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Bezahlt</h3>
              <p className="text-2xl font-bold text-green-600">{paymentAnalytics.paid_certificates}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Fehlgeschlagen</h3>
              <p className="text-2xl font-bold text-red-600">{paymentAnalytics.failed_certificates}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Abgelaufen</h3>
              <p className="text-2xl font-bold text-yellow-600">{paymentAnalytics.expired_certificates}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Streitig</h3>
              <p className="text-2xl font-bold text-purple-600">{paymentAnalytics.disputed_certificates}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Unbezahlt</h3>
              <p className="text-2xl font-bold text-gray-600">{paymentAnalytics.unpaid_certificates}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Gesamtumsatz</h3>
              <p className="text-2xl font-bold text-green-600">{formatCurrency(paymentAnalytics.total_revenue_cents)}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow border">
              <h3 className="text-sm font-medium text-gray-500">Konversionsrate</h3>
              <p className="text-2xl font-bold text-blue-600">{paymentAnalytics.conversion_rate}%</p>
            </div>
          </div>
        ) : (
          <ErrorMessage message="Fehler beim Laden der Zahlungsanalyse" />
        )}
      </div>



      {/* Webhook Health Monitor Section */}
      <div className="mb-8">
        <WebhookHealthMonitor />
      </div>
      
      {/* Webhook Events Section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Webhook-Events (letzte 7 Tage)</h2>
        {webhookLoading ? (
          <LoadingSpinner message="Lade Webhook-Events..." />
        ) : webhookEvents && webhookEvents.length > 0 ? (
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event-Typ</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Anzahl</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Letztes Event</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {webhookEvents.map((event, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{event.event_type}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        event.processing_status === 'handled' ? 'bg-green-100 text-green-800' :
                        event.processing_status === 'error' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {event.processing_status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{event.count}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(event.latest_event).toLocaleString('de-DE')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-500">Keine Webhook-Events in den letzten 7 Tagen gefunden.</p>
        )}
      </div>

      {/* Payment Attempts Section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Zahlungsversuche (letzte 7 Tage)</h2>
        {attemptsLoading ? (
          <LoadingSpinner message="Lade Zahlungsversuche..." />
        ) : paymentAttempts && paymentAttempts.length > 0 ? (
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Anzahl</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ø Sitzungsdauer</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Letzter Versuch</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paymentAttempts.map((attempt, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        attempt.attempt_status === 'succeeded' ? 'bg-green-100 text-green-800' :
                        attempt.attempt_status === 'failed' ? 'bg-red-100 text-red-800' :
                        attempt.attempt_status === 'abandoned' ? 'bg-yellow-100 text-yellow-800' :
                        attempt.attempt_status === 'expired' ? 'bg-gray-100 text-gray-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {attempt.attempt_status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{attempt.count}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {attempt.avg_session_duration > 0 ? formatDuration(Math.round(attempt.avg_session_duration)) : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(attempt.latest_attempt).toLocaleString('de-DE')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-gray-500">Keine Zahlungsversuche in den letzten 7 Tagen gefunden.</p>
        )}
      </div>

      {/* Pricing Management Section */}
      <div className="mt-8">
        <PricingManagement />
      </div>

    </div>
  );
};
