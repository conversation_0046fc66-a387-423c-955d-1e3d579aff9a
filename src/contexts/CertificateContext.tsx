import { createContext, useContext, useState, useEffect, useMemo, useCallback } from 'react';
import { useQueries, useQuery, useQueryClient } from '@tanstack/react-query';
import type { ReactNode } from 'react';
import { supabase } from '../lib/supabase';

// Define the context type
import { type EnergieausweisData } from '../types/csv';
import { listCertificateFiles } from '../utils/fileUtils';
import { PricingService } from '../services/pricingService';

console.log('🚀 CertificateContext loaded')
// Enhanced context interface for consolidated data fetching
interface CertificateContextType {
  // Certificate Management
  activeCertificateId: string | null;
  setActiveCertificateId: (id: string | null) => void;

  // Consolidated Data State
  certificateData: EnergieausweisData | null;
  certificateFiles: Record<string, any[]>;
  pricingInfo: any;

  // Loading States
  isLoading: boolean;
  isDataLoading: boolean;
  isFilesLoading: boolean;
  isPricingLoading: boolean;

  // Error States
  error: string | null;

  // Actions
  createNewCertificate: (certificateType: string) => Promise<string>;
  refreshCertificateData: () => Promise<void>;
  invalidateCache: () => void;
}

// --- Module-scoped fetch helpers ---
const fetchCertificateData = async (certificateId: string | null) => {
  if (!certificateId) return null;
  const { data, error } = await supabase
    .from('energieausweise')
    .select('*')
    .eq('id', certificateId)
    .single();

  if (error) throw error;
  return data as EnergieausweisData;
};

const fetchCertificateFiles = async (certificateId: string | null) => {
  if (!certificateId) return [];
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) return [];
    return await listCertificateFiles(user.user.id, certificateId);
  } catch (err) {
    console.error('Error fetching certificate files', err);
    return [];
  }
};

const fetchUserData = async () => {
  const { data: user } = await supabase.auth.getUser();
  return user.user || null;
};

// Hook that batches related certificate queries in parallel
const useCertificateDataBatch = (certificateId: string | null) => {
  const results = useQueries({
    queries: [
      {
        queryKey: ['certificate', certificateId],
        queryFn: () => fetchCertificateData(certificateId),
        enabled: !!certificateId,
        staleTime: 15 * 60 * 1000,
      },
      {
        queryKey: ['certificate-files', certificateId],
        queryFn: () => fetchCertificateFiles(certificateId),
        enabled: !!certificateId,
        staleTime: 10 * 60 * 1000,
      },
      {
        queryKey: ['user-data'],
        queryFn: () => fetchUserData(),
        staleTime: Infinity,
      }
    ]
  });

  return {
    data: {
      certificate: results[0]?.data ?? null,
      files: results[1]?.data ?? [],
      user: results[2]?.data ?? null,
    },
    isLoading: results.some(r => r.isLoading),
    isError: results.some(r => r.isError),
    errors: results.map(r => r.error).filter(Boolean),
    results
  };
};

// Create the context with default values
const CertificateContext = createContext<CertificateContextType>({
  activeCertificateId: null,
  setActiveCertificateId: () => {},

  certificateData: null,
  certificateFiles: {},
  pricingInfo: null,

  isLoading: true,
  isDataLoading: true,
  isFilesLoading: true,
  isPricingLoading: true,

  error: null,

  createNewCertificate: async () => '',
  refreshCertificateData: async () => {},
  invalidateCache: () => {},
});

// Create a provider component
export const CertificateProvider = ({ children }: { children: ReactNode }) => {
  const [activeCertificateId, setActiveCertificateId] = useState<string | null>(null);
  const [mountedLoading, setMountedLoading] = useState(true);
  const queryClient = useQueryClient();

  
  // Load the active certificate ID from localStorage on mount
  useEffect(() => {
    const storedId = localStorage.getItem('activeCertificateId');
    if (storedId) {
      setActiveCertificateId(storedId);
    }
    setMountedLoading(false);
  }, []);

  // Update localStorage when the active certificate ID changes
  useEffect(() => {
    if (activeCertificateId) {
      localStorage.setItem('activeCertificateId', activeCertificateId);
    } else {
      localStorage.removeItem('activeCertificateId');
    }
  }, [activeCertificateId]);

  // Function to create a new certificate - memoized to prevent recreation
  const createNewCertificate = useCallback(async (certificateType: string): Promise<string> => {
    try {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('Nicht eingeloggt');

      // First create the certificate to get the ID
      const { data, error } = await supabase
        .from('energieausweise')
        .insert({
          user_id: user.user.id,
          certificate_type: certificateType,
          status: 'objektdaten',
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;

      // Set the new certificate as active and generate order number
      if (data && data.id) {
        const certificateId = data.id;
        const orderNumber = `EA-${certificateId.slice(-8).toUpperCase()}`;

        // Update the certificate with the order number
        const { error: updateError } = await supabase
          .from('energieausweise')
          .update({ order_number: orderNumber })
          .eq('id', certificateId);

        if (updateError) {
          console.error('Error updating order number:', updateError);
          // Don't throw here - the certificate was created successfully
          // The order number can be generated later if needed
        }

        setActiveCertificateId(certificateId);
        return certificateId;
      }

      throw new Error('Fehler beim Erstellen des Zertifikats');
    } catch (error) {
      console.error('Error creating certificate:', error);
      throw error;
    }
  }, []);

  // Use the batch hook to fetch current certificate data
  const batch = useCertificateDataBatch(activeCertificateId);

  // Pricing info: reuse existing PricingService (keeps compatibility with other code)
  const { data: pricingInfo, isLoading: isPricingLoading } = useQuery({
    queryKey: ['pricing', batch.data.certificate?.certificate_type],
    queryFn: async () => {
      if (!batch.data.certificate?.certificate_type) return null;
      return await PricingService.getPricingDisplayInfoForType(batch.data.certificate!.certificate_type as any);
    },
    enabled: !!batch.data.certificate?.certificate_type,
    staleTime: 15 * 60 * 1000,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  const isFilesLoading = batch.results?.[1]?.isLoading ?? false;
  const isDataLoading = batch.results?.[0]?.isLoading ?? false;

  const error = batch.isError ? (batch.errors[0] as any)?.message || 'Fetch error' : null;
  const isLoading = mountedLoading || isDataLoading || isFilesLoading || isPricingLoading;

  const refreshCertificateData = useCallback(async () => {
    if (!activeCertificateId) return;
    await queryClient.invalidateQueries({ queryKey: ['certificate', activeCertificateId] });
    await queryClient.invalidateQueries({ queryKey: ['certificate-files', activeCertificateId] });
  }, [activeCertificateId, queryClient]);

  const invalidateCache = useCallback(() => {
    queryClient.clear();
  }, [queryClient]);

  // Create the context value object - memoized to prevent unnecessary re-renders
  const contextValue: CertificateContextType = useMemo(() => ({
    activeCertificateId,
    setActiveCertificateId,

    certificateData: batch.data.certificate ?? null,
    certificateFiles: (batch.data.files || []).reduce((acc: Record<string, any[]>, file: any) => {
      const key = file.fieldName || 'unknown';
      acc[key] = acc[key] || [];
      acc[key].push(file);
      return acc;
    }, {}),
    pricingInfo: pricingInfo ?? null,

  isLoading,
    isDataLoading,
    isFilesLoading,
    isPricingLoading,

    error,

    createNewCertificate,
    refreshCertificateData,
    invalidateCache,
  }), [activeCertificateId, setActiveCertificateId, batch, isLoading, isDataLoading, isFilesLoading, isPricingLoading, pricingInfo, createNewCertificate, refreshCertificateData, invalidateCache, error]);

  // Provide the context to children
  return (
    <CertificateContext.Provider value={contextValue}>
      {children}
    </CertificateContext.Provider>
  );
};

// Create a custom hook to use the certificate context
export const useCertificate = () => useContext(CertificateContext);
