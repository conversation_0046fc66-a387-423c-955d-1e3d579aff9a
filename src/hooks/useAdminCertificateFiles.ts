import { useQuery } from '@tanstack/react-query';
import { getAdminCertificateFiles, type CertificateFileStats } from '../utils/adminFileUtils';

/**
 * Hook for managing certificate files in admin context
 * This hook can access files for any user/certificate combination
 */
export const useAdminCertificateFiles = (userId: string | null, certificateId: string | null) => {
  const {
    data: fileStats,
    isLoading,
    error,
    refetch
  } = useQuery<CertificateFileStats>({
    queryKey: ['admin-certificate-files', userId, certificateId],
    queryFn: async () => {
      if (!userId || !certificateId) {
        return {
          totalFiles: 0,
          totalSize: 0,
          buildingImages: 0,
          consumptionBills: 0,
          floorPlans: 0,
          categories: {
            building_images: [],
            consumption_bills: [],
            floor_plans: []
          }
        };
      }
      return await getAdminCertificateFiles(userId, certificateId);
    },
    enabled: !!userId && !!certificateId,
    staleTime: 30 * 1000, // 30 seconds - longer than user context since admin doesn't modify files
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
    refetchOnWindowFocus: false, // Don't refetch on focus for admin
    retry: 2, // Fewer retries for admin context
    refetchOnMount: false, // Only refetch if data is stale
    refetchOnReconnect: false, // Don't refetch on network reconnect
    retryDelay: 1000, // Shorter delay between retries
    refetchInterval: false, // Disable automatic refetching
    refetchIntervalInBackground: false, // Disable refetching in background
  });

  // Helper functions
  const hasFiles = fileStats ? fileStats.totalFiles > 0 : false;
  const hasBuildingImages = fileStats ? fileStats.buildingImages > 0 : false;
  const hasConsumptionBills = fileStats ? fileStats.consumptionBills > 0 : false;
  const hasFloorPlans = fileStats ? fileStats.floorPlans > 0 : false;

  return {
    fileStats: fileStats || {
      totalFiles: 0,
      totalSize: 0,
      buildingImages: 0,
      consumptionBills: 0,
      floorPlans: 0,
      categories: {
        building_images: [],
        consumption_bills: [],
        floor_plans: []
      }
    },
    isLoading,
    error,
    refetch,
    hasFiles,
    hasBuildingImages,
    hasConsumptionBills,
    hasFloorPlans
  };
};
