import { useQuery } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import { useCertificate } from '../contexts/CertificateContext';

// Define certificate types
export type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

/**
 * Custom hook to fetch the certificate type for the active certificate
 * @returns Object containing certificate type data, loading state, and error state
 */
export const useCertificateType = () => {
  const { activeCertificateId } = useCertificate();

  const {
    data: certificateData,
    isLoading,
    isError,
    error
  } = useQuery({
    queryKey: ['energieausweise', 'certificate_type', activeCertificateId],
    queryFn: async () => {
      if (!activeCertificateId) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('certificate_type')
        .eq('id', activeCertificateId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!activeCertificateId,
    retry: false,
    staleTime: 10 * 60 * 1000, // Increased to 10 minutes - certificate type rarely changes
    gcTime: 15 * 60 * 1000, // Keep in cache longer
    refetchOnWindowFocus: false, // Prevent refetch on tab switching
    refetchOnMount: false, // Prevent refetch on component remount
  });

  return {
    certificateType: certificateData?.certificate_type as CertificateType | null,
    isLoading,
    isError,
    error,
  };
};
