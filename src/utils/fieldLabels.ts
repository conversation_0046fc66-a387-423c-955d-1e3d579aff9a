/**
 * Field label mappings for energy certificate data display
 * Maps technical field names to user-friendly German labels
 */

export const fieldLabels: Record<string, string> = {
  // Bestellinformationen
  order_number: 'Bestellnummer',

  // Objektdaten
  Strasse: 'Straße',
  Hausnr: 'Hausnummer',
  PLZ: 'PLZ',
  Ort: 'Ort',
  WSchVo77_erfuellt: 'Wärmeschutzverordnung 1977 erfüllt',
  gebaeudebild: 'Gebäudebild',

  // Kundendaten
  Kunden_Anrede: 'Anrede',
  Kunden_Vorname: 'Vorname',
  Kunden_Nachname: 'Nachname',
  Kunden_Strasse: 'Straße',
  Kunden_Hausnr: 'Hausnummer',
  Kunden_PLZ: 'PLZ',
  Kunden_Ort: 'Ort',
  Kunden_email: 'E-Mail',
  Kunden_telefon: 'Telefon',

  // Gebäudedetails 1
  BedarfVerbrauch: 'Bedarf/Verbrauch',
  Anlass: 'Anlass der Ausstellung',
  Datenerhebung: 'Datenerhebung durch',
  nichtWohnGeb: 'G<PERSON><PERSON>udety<PERSON>',
  isGebaeudehuelle: 'Gebäudehülle',
  Nutzung1_ID: 'Nutzung1 ID',
  Nutzung1_Flaeche: 'Nutzung1 Fläche (m²)',
  Baujahr: 'Baujahr',
  Modernisierung: 'Jahr der Modernisierung',
  Wohnfläche: 'Wohnfläche (m²)',
  Raumhöhe: 'Raumhöhe (m)',
  Volumen: 'Gebäudevolumen (m³)',
  Wohneinheiten: 'Anzahl Wohneinheiten',
  Geschosse: 'Anzahl Geschosse',
  anbauSituation: 'Anbausituation',
  Keller_beheizt: 'Keller beheizt',
  Klimatisiert: 'Klimatisiert',
  ergaenzendeErlaeuterungen: 'Ergänzende Erläuterungen',
  baujahrHzErz: 'Baujahr Heizung',

  // Gebäudedetails 2
  Gebaeudeart: 'Gebäudeart',
  Gebaeudekategorie: 'Gebäudekategorie',
  Gebaeudetyp: 'Gebäudetyp',
  Gebaeudeform: 'Gebäudeform',
  Dachform: 'Dachform',
  Dachgeschossausbau: 'Dachgeschossausbau',
  Kellergeschoss: 'Kellergeschoss',
  kuehlWfl: 'Kühlfläche (m²)',
  Originaldaemmstandard: 'Originaldämmstandard',
  bjFensterAustausch: 'Jahr des Fensteraustauschs',
  Fensterlüftung: 'Fensterlüftung',
  Schachtlüftung: 'Schachtlüftung',
  L_Mit_WRG: 'Lüftungsanlage mit Wärmerückgewinnung',
  L_Ohne_WRG: 'Lüftungsanlage ohne Wärmerückgewinnung',

  // Bauteile
  boeden: 'Böden',
  daecher: 'Dächer',
  waende: 'Wände',
  bezeichnung: 'Bezeichnung',
  massiv: 'Material',
  uebergang: 'Übergang',
  flaeche: 'Fläche (m²)',
  daemmung: 'Dämmung (cm)',

  // Böden (dynamisch)
  Boden1: 'Boden 1 - Bezeichnung',
  Boden1_massiv: 'Boden 1 - Material',
  Boden1_Kellerdecke: 'Boden 1 - Übergang zu unbeheiztem Keller',
  Boden1_Fläche: 'Boden 1 - Fläche (m²)',
  Boden1_Dämmung: 'Boden 1 - Dämmung (cm)',

  Boden2: 'Boden 2 - Bezeichnung',
  Boden2_massiv: 'Boden 2 - Material',
  Boden2_Kellerdecke: 'Boden 2 - Übergang zu unbeheiztem Keller',
  Boden2_Fläche: 'Boden 2 - Fläche (m²)',
  Boden2_Dämmung: 'Boden 2 - Dämmung (cm)',

  // Dächer (dynamisch)
  Dach1: 'Dach 1 - Bezeichnung',
  Dach1_massiv: 'Dach 1 - Material',
  Dach1_Geschossdecke: 'Dach 1 - Übergang zu unbeheiztem Dachraum',
  Dach1_Fläche: 'Dach 1 - Fläche (m²)',
  Dach1_Dämmung: 'Dach 1 - Dämmung (cm)',

  Dach2: 'Dach 2 - Bezeichnung',
  Dach2_massiv: 'Dach 2 - Material',
  Dach2_Geschossdecke: 'Dach 2 - Übergang zu unbeheiztem Dachraum',
  Dach2_Fläche: 'Dach 2 - Fläche (m²)',
  Dach2_Dämmung: 'Dach 2 - Dämmung (cm)',

  // Wände (dynamisch)
  Wand1: 'Wand 1 - Bezeichnung',
  Wand1_massiv: 'Wand 1 - Material',
  Wand1_Fläche: 'Wand 1 - Fläche (m²)',
  Wand1_Dämmung: 'Wand 1 - Dämmung (cm)',

  Wand2: 'Wand 2 - Bezeichnung',
  Wand2_massiv: 'Wand 2 - Material',
  Wand2_Fläche: 'Wand 2 - Fläche (m²)',
  Wand2_Dämmung: 'Wand 2 - Dämmung (cm)',

  // Fenster
  Fensterart: 'Fensterart',
  Verglasung: 'Verglasung',
  Rahmen: 'Rahmen',

  // Dynamische Fenster
  Fenster1_bezeichnung: 'Fenster 1 - Bezeichnung',
  Fenster1_art: 'Fenster 1 - Art',
  Fenster1_flaeche: 'Fenster 1 - Fläche (m²)',
  Fenster1_ausrichtung: 'Fenster 1 - Ausrichtung',
  Fenster1_baujahr: 'Fenster 1 - Baujahr',

  Fenster2_bezeichnung: 'Fenster 2 - Bezeichnung',
  Fenster2_art: 'Fenster 2 - Art',
  Fenster2_flaeche: 'Fenster 2 - Fläche (m²)',
  Fenster2_ausrichtung: 'Fenster 2 - Ausrichtung',
  Fenster2_baujahr: 'Fenster 2 - Baujahr',

  Fenster3_bezeichnung: 'Fenster 3 - Bezeichnung',
  Fenster3_art: 'Fenster 3 - Art',
  Fenster3_flaeche: 'Fenster 3 - Fläche (m²)',
  Fenster3_ausrichtung: 'Fenster 3 - Ausrichtung',
  Fenster3_baujahr: 'Fenster 3 - Baujahr',

  // Heizung
  Hzg_Speicher_Baujahr: 'Baujahr Pufferspeicher',
  Hzg_Verteilung_Baujahr: 'Baujahr Verteilleitungen',
  Hzg_Übergabe: 'Wärmeübergabe',
  Hzg_Verteilung_Art: 'Art der Wärmeverteilung',
  Hzg_kreistemperatur: 'Heizkreistemperatur',
  Hzg_Verteilung_Dämmung: 'Dämmung der Verteilleitungen',
  Hzg_Speicher: 'Pufferspeicher vorhanden',
  Hzg_Aufstellung: 'Aufstellungsort der Heizung',
  Hzg_Technik: 'Heiztechnik',
  Hzg_Energieträger: 'Energieträger',
  Hzg_PrimFaktor: 'Primärenergiefaktor',

  // Trinkwarmwasser & Lüftung
  TW_Baujahr: 'Baujahr der Trinkwarmwasseranlage',
  TW_Speicher_Baujahr: 'Baujahr des TW-Speichers',
  TW_Verteilung_Baujahr: 'Baujahr der TW-Leitungen',
  TW_Verteilung_Art: 'Art der TW-Verteilung',
  TW_Verteilung_Dämmung: 'Dämmung der TW-Leitungen',
  TW_Zirkulation: 'Trinkwasser-Zirkulation',
  TW_Speicher_Standort: 'Standort des TW-Speichers',
  TW_Technik: 'Technik der Trinkwarmwasserbereitung',
  TW_Solar: 'Solaranlage für Trinkwarmwasser',
  TW_WP: 'Wärmepumpe für Trinkwarmwasser',
  HZ_Solar: 'Solaranlage für Heizung',
  HZ_WP: 'Wärmepumpe für Heizung',
  Luft_Baujahr: 'Baujahr der Lüftungsanlage',
  Luft_Verteilung_Baujahr: 'Baujahr der Luftleitungen',
  Luft_Lage: 'Lage der Lüftungsanlage',
  Luft_Typ: 'Typ der Lüftungsanlage',
  Lueftungskonzept: 'Lüftungskonzept',
  Lueftungsanlage: 'Lüftungsanlage',

  // Verbrauchsdaten
  Abrechnungszeitraum: 'Abrechnungszeitraum',
  Verbrauch_Heizung: 'Verbrauch Heizung',
  Verbrauch_Warmwasser: 'Verbrauch Warmwasser',
  Energietraeger_Verbrauch: 'Energieträger (Verbrauch)',
  Verbrauchserfassung: 'Verbrauchserfassung',
  Leerstand: 'Leerstand',
  Warmwasseranteil: 'Warmwasseranteil',
  Rechnungen: 'Verbrauchsrechnungen',

  // Energieträger 1 (ETr1)
  ETr1_Kategorie: 'Energieträgerkategorie 1',
  ETr1_Heizung: 'Für Heizung verwendet',
  ETr1_TWW: 'Für Trinkwasser verwendet 1',
  ETr1_ZusatzHz: 'Zusatzheizung',
  ETr1_Lueften: 'Für Lüftung verwendet',
  ETr1_Licht: 'Für Licht verwendet',
  ETr1_Kuehlen: 'Für Kühlung verwendet',
  ETr1_Sonst: 'Für sonstige Verwendung',
  ETr1_PrimFaktor: 'Primärenergiefaktor',
  ETr1_Anteil_erneuerbar: 'Anteil erneuerbar',
  ETr1_Anteil_KWK: 'KWK-Anteil',
  ETr1_isFw: 'Energieträger ist Fernwärme',
  ETr1_gebaeudeNahErzeugt: 'Energie gebäudenah erzeugt',
  ETr1_Name: 'Name des Energieträgers 1',

  // Verbrauchszeiträume für ETr1
  ETr1_Jahr1_von: 'Beginn Messperiode 1',
  ETr1_Jahr1_bis: 'Ende Messperiode 1',
  ETr1_Jahr1_Menge: 'Gesamtmenge Periode 1',
  ETr1_Jahr1_Menge_TWW: 'Menge für Trinkwasser Periode 1',
  ETr1_Jahr1_Leerstand: 'Leerstand in % der Periode 1',
  ETr1_Jahr1_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 1',
  ETr1_Jahr1_Leerstand_von: 'Leerstand Beginn Periode 1',
  ETr1_Jahr1_Leerstand_bis: 'Leerstand Ende Periode 1',

  ETr1_Jahr2_von: 'Beginn Messperiode 2',
  ETr1_Jahr2_bis: 'Ende Messperiode 2',
  ETr1_Jahr2_Menge: 'Gesamtmenge Periode 2',
  ETr1_Jahr2_Menge_TWW: 'Menge für Trinkwasser Periode 2',
  ETr1_Jahr2_Leerstand: 'Leerstand in % der Periode 2',
  ETr1_Jahr2_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 2',
  ETr1_Jahr2_Leerstand_von: 'Leerstand Beginn Periode 2',
  ETr1_Jahr2_Leerstand_bis: 'Leerstand Ende Periode 2',

  ETr1_Jahr3_von: 'Beginn Messperiode 3',
  ETr1_Jahr3_bis: 'Ende Messperiode 3',
  ETr1_Jahr3_Menge: 'Gesamtmenge Periode 3',
  ETr1_Jahr3_Menge_TWW: 'Menge für Trinkwasser Periode 3',
  ETr1_Jahr3_Leerstand: 'Leerstand in % der Periode 3',
  ETr1_Jahr3_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 3',
  ETr1_Jahr3_Leerstand_von: 'Leerstand Beginn Periode 3',
  ETr1_Jahr3_Leerstand_bis: 'Leerstand Ende Periode 3',

  // Energieträger 2 (ETr2)
  ETr2_Kategorie: 'Energieträgerkategorie 2',
  ETr2_Heizung: 'Für Heizung verwendet',
  ETr2_TWW: 'Für Trinkwasser verwendet 2',
  ETr2_ZusatzHz: 'Zusatzheizung',
  ETr2_Lueften: 'Für Lüftung verwendet',
  ETr2_Licht: 'Für Licht verwendet',
  ETr2_Kuehlen: 'Für Kühlung verwendet',
  ETr2_Sonst: 'Für sonstige Verwendung',
  ETr2_PrimFaktor: 'Primärenergiefaktor',
  ETr2_Anteil_erneuerbar: 'Anteil erneuerbar',
  ETr2_Anteil_KWK: 'KWK-Anteil',
  ETr2_isFw: 'Energieträger ist Fernwärme',
  ETr2_gebaeudeNahErzeugt: 'Energie gebäudenah erzeugt',
  ETr2_Name: 'Name des Energieträgers 2',

  // Verbrauchszeiträume für ETr2
  ETr2_Jahr1_von: 'Beginn Messperiode 1',
  ETr2_Jahr1_bis: 'Ende Messperiode 1',
  ETr2_Jahr1_Menge: 'Gesamtmenge Periode 1',
  ETr2_Jahr1_Menge_TWW: 'Menge für Trinkwasser Periode 1',
  ETr2_Jahr1_Leerstand: 'Leerstand in % der Periode 1',
  ETr2_Jahr1_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 1',
  ETr2_Jahr1_Leerstand_von: 'Leerstand Beginn Periode 1',
  ETr2_Jahr1_Leerstand_bis: 'Leerstand Ende Periode 1',

  ETr2_Jahr2_von: 'Beginn Messperiode 2',
  ETr2_Jahr2_bis: 'Ende Messperiode 2',
  ETr2_Jahr2_Menge: 'Gesamtmenge Periode 2',
  ETr2_Jahr2_Menge_TWW: 'Menge für Trinkwasser Periode 2',
  ETr2_Jahr2_Leerstand: 'Leerstand in % der Periode 2',
  ETr2_Jahr2_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 2',
  ETr2_Jahr2_Leerstand_von: 'Leerstand Beginn Periode 2',
  ETr2_Jahr2_Leerstand_bis: 'Leerstand Ende Periode 2',

  ETr2_Jahr3_von: 'Beginn Messperiode 3',
  ETr2_Jahr3_bis: 'Ende Messperiode 3',
  ETr2_Jahr3_Menge: 'Gesamtmenge Periode 3',
  ETr2_Jahr3_Menge_TWW: 'Menge für Trinkwasser Periode 3',
  ETr2_Jahr3_Leerstand: 'Leerstand in % der Periode 3',
  ETr2_Jahr3_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 3',
  ETr2_Jahr3_Leerstand_von: 'Leerstand Beginn Periode 3',
  ETr2_Jahr3_Leerstand_bis: 'Leerstand Ende Periode 3',

  // Energieträger 3 (ETr3)
  ETr3_Kategorie: 'Energieträgerkategorie 3',
  ETr3_Heizung: 'Für Heizung verwendet',
  ETr3_TWW: 'Für Trinkwasser verwendet 3',
  ETr3_ZusatzHz: 'Zusatzheizung',
  ETr3_Lueften: 'Für Lüftung verwendet',
  ETr3_Licht: 'Für Licht verwendet',
  ETr3_Kuehlen: 'Für Kühlung verwendet',
  ETr3_Sonst: 'Für sonstige Verwendung',
  ETr3_PrimFaktor: 'Primärenergiefaktor',
  ETr3_Anteil_erneuerbar: 'Anteil erneuerbar',
  ETr3_Anteil_KWK: 'KWK-Anteil',
  ETr3_isFw: 'Energieträger ist Fernwärme',
  ETr3_gebaeudeNahErzeugt: 'Energie gebäudenah erzeugt',
  ETr3_Name: 'Name des Energieträgers 3',

  // Verbrauchszeiträume für ETr3
  ETr3_Jahr1_von: 'Beginn Messperiode 1',
  ETr3_Jahr1_bis: 'Ende Messperiode 1',
  ETr3_Jahr1_Menge: 'Gesamtmenge Periode 1',
  ETr3_Jahr1_Menge_TWW: 'Menge für Trinkwasser Periode 1',
  ETr3_Jahr1_Leerstand: 'Leerstand in % der Periode 1',
  ETr3_Jahr1_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 1',
  ETr3_Jahr1_Leerstand_von: 'Leerstand Beginn Periode 1',
  ETr3_Jahr1_Leerstand_bis: 'Leerstand Ende Periode 1',

  ETr3_Jahr2_von: 'Beginn Messperiode 2',
  ETr3_Jahr2_bis: 'Ende Messperiode 2',
  ETr3_Jahr2_Menge: 'Gesamtmenge Periode 2',
  ETr3_Jahr2_Menge_TWW: 'Menge für Trinkwasser Periode 2',
  ETr3_Jahr2_Leerstand: 'Leerstand in % der Periode 2',
  ETr3_Jahr2_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 2',
  ETr3_Jahr2_Leerstand_von: 'Leerstand Beginn Periode 2',
  ETr3_Jahr2_Leerstand_bis: 'Leerstand Ende Periode 2',

  ETr3_Jahr3_von: 'Beginn Messperiode 3',
  ETr3_Jahr3_bis: 'Ende Messperiode 3',
  ETr3_Jahr3_Menge: 'Gesamtmenge Periode 3',
  ETr3_Jahr3_Menge_TWW: 'Menge für Trinkwasser Periode 3',
  ETr3_Jahr3_Leerstand: 'Leerstand in % der Periode 3',
  ETr3_Jahr3_Leerstand_hasLeerstand: 'Leerstand vorhanden Periode 3',
  ETr3_Jahr3_Leerstand_von: 'Leerstand Beginn Periode 3',
  ETr3_Jahr3_Leerstand_bis: 'Leerstand Ende Periode 3',

  // Verbrauchsrechnungen
  verbrauchsrechnung1: 'Verbrauchsrechnung 1',
  verbrauchsrechnung2: 'Verbrauchsrechnung 2',
  verbrauchsrechnung3: 'Verbrauchsrechnung 3',

  // Grundrisse
  grundriss: 'Grundrisse'
};