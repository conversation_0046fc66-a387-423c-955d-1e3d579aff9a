import { describe, it, expect } from 'vitest';

// Extract the date calculation functions for testing
const calculateDateWithYearOffset = (dateString: string, yearOffset: number, dayOffset: number = 0): string => {
  if (!dateString) return '';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';

  // Add or subtract the specified number of years
  date.setFullYear(date.getFullYear() + yearOffset);
  
  // Add or subtract the specified number of days
  if (dayOffset !== 0) {
    date.setDate(date.getDate() + dayOffset);
  }

  // Return in YYYY-MM-DD format for date inputs
  return date.toISOString().split('T')[0];
};

const calculateAllDatesFromEndDate = (endDate: string): {
  jahr1Von: string;
  jahr1Bis: string;
  jahr2Von: string;
  jahr2Bis: string;
  jahr3Von: string;
  jahr3Bis: string;
} => {
  if (!endDate) {
    return {
      jahr1Von: '',
      jahr1Bis: '',
      jahr2Von: '',
      jahr2Bis: '',
      jahr3Von: '',
      jahr3Bis: ''
    };
  }

  // The input date is the END of the most recent consumption period (Jahr 1)
  // Calculate three consecutive 12-month periods going backwards
  const jahr1Bis = endDate; // Most recent period ends on the input date
  const jahr1Von = calculateDateWithYearOffset(endDate, -1, 1); // Start 1 year before, +1 day
  const jahr2Bis = calculateDateWithYearOffset(endDate, -1); // Second period ends 1 year before
  const jahr2Von = calculateDateWithYearOffset(endDate, -2, 1); // Start 2 years before, +1 day
  const jahr3Bis = calculateDateWithYearOffset(endDate, -2); // Third period ends 2 years before
  const jahr3Von = calculateDateWithYearOffset(endDate, -3, 1); // Start 3 years before, +1 day

  return {
    jahr1Von,
    jahr1Bis,
    jahr2Von,
    jahr2Bis,
    jahr3Von,
    jahr3Bis
  };
};

describe('Date Calculations for Energy Consumption Periods', () => {
  describe('calculateDateWithYearOffset', () => {
    it('should calculate date with year offset correctly', () => {
      expect(calculateDateWithYearOffset('2024-01-15', -1)).toBe('2023-01-15');
      expect(calculateDateWithYearOffset('2024-01-15', -2)).toBe('2022-01-15');
      expect(calculateDateWithYearOffset('2024-01-15', -3)).toBe('2021-01-15');
    });

    it('should calculate date with year and day offset correctly', () => {
      expect(calculateDateWithYearOffset('2024-01-15', -1, 1)).toBe('2023-01-16');
      expect(calculateDateWithYearOffset('2024-01-15', -2, 1)).toBe('2022-01-16');
      expect(calculateDateWithYearOffset('2024-01-15', -3, 1)).toBe('2021-01-16');
    });

    it('should handle empty input', () => {
      expect(calculateDateWithYearOffset('', -1)).toBe('');
    });

    it('should handle invalid date input', () => {
      expect(calculateDateWithYearOffset('invalid-date', -1)).toBe('');
    });
  });

  describe('calculateAllDatesFromEndDate', () => {
    it('should calculate three consecutive 12-month periods backwards from end date', () => {
      const result = calculateAllDatesFromEndDate('2024-01-15');
      
      // Jahr 1 (most recent): 2023-01-16 to 2024-01-15
      expect(result.jahr1Von).toBe('2023-01-16');
      expect(result.jahr1Bis).toBe('2024-01-15');
      
      // Jahr 2: 2022-01-16 to 2023-01-15
      expect(result.jahr2Von).toBe('2022-01-16');
      expect(result.jahr2Bis).toBe('2023-01-15');
      
      // Jahr 3 (oldest): 2021-01-16 to 2022-01-15
      expect(result.jahr3Von).toBe('2021-01-16');
      expect(result.jahr3Bis).toBe('2022-01-15');
    });

    it('should handle different end dates correctly', () => {
      const result = calculateAllDatesFromEndDate('2024-12-31');
      
      // Jahr 1 (most recent): 2024-01-01 to 2024-12-31
      expect(result.jahr1Von).toBe('2024-01-01');
      expect(result.jahr1Bis).toBe('2024-12-31');
      
      // Jahr 2: 2023-01-01 to 2023-12-31
      expect(result.jahr2Von).toBe('2023-01-01');
      expect(result.jahr2Bis).toBe('2023-12-31');
      
      // Jahr 3 (oldest): 2022-01-01 to 2022-12-31
      expect(result.jahr3Von).toBe('2022-01-01');
      expect(result.jahr3Bis).toBe('2022-12-31');
    });

    it('should ensure all periods are in the past relative to current date', () => {
      const today = new Date().toISOString().split('T')[0];
      const result = calculateAllDatesFromEndDate(today);

      // All calculated dates should be in the past or today
      const currentTime = new Date(today).getTime();

      expect(new Date(result.jahr1Von).getTime()).toBeLessThanOrEqual(currentTime);
      expect(new Date(result.jahr1Bis).getTime()).toBeLessThanOrEqual(currentTime);
      expect(new Date(result.jahr2Von).getTime()).toBeLessThan(currentTime);
      expect(new Date(result.jahr2Bis).getTime()).toBeLessThan(currentTime);
      expect(new Date(result.jahr3Von).getTime()).toBeLessThan(currentTime);
      expect(new Date(result.jahr3Bis).getTime()).toBeLessThan(currentTime);
    });

    it('should handle empty input', () => {
      const result = calculateAllDatesFromEndDate('');
      
      expect(result.jahr1Von).toBe('');
      expect(result.jahr1Bis).toBe('');
      expect(result.jahr2Von).toBe('');
      expect(result.jahr2Bis).toBe('');
      expect(result.jahr3Von).toBe('');
      expect(result.jahr3Bis).toBe('');
    });

    it('should create consecutive periods with no gaps', () => {
      const result = calculateAllDatesFromEndDate('2024-06-30');
      
      // Jahr 2 should end one day before Jahr 1 starts
      const jahr1Start = new Date(result.jahr1Von);
      const jahr2End = new Date(result.jahr2Bis);
      const dayDifference = (jahr1Start.getTime() - jahr2End.getTime()) / (1000 * 60 * 60 * 24);
      expect(dayDifference).toBe(1);
      
      // Jahr 3 should end one day before Jahr 2 starts
      const jahr2Start = new Date(result.jahr2Von);
      const jahr3End = new Date(result.jahr3Bis);
      const dayDifference2 = (jahr2Start.getTime() - jahr3End.getTime()) / (1000 * 60 * 60 * 24);
      expect(dayDifference2).toBe(1);
    });
  });
});
