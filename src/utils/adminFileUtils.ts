import { supabase } from '../lib/supabase';
import { listCertificateFiles, getSignedUrl, type StorageFileInfo } from './fileUtils';
import JSZip from 'jszip';

/**
 * File category types for admin display
 */
export type FileCategory = 'building_images' | 'consumption_bills' | 'floor_plans';

/**
 * Categorized file information for admin display
 */
export interface CategorizedFileInfo extends StorageFileInfo {
  category: FileCategory;
  displayName: string;
}

/**
 * File statistics for a certificate
 */
export interface CertificateFileStats {
  totalFiles: number;
  totalSize: number;
  buildingImages: number;
  consumptionBills: number;
  floorPlans: number;
  categories: {
    building_images: CategorizedFileInfo[];
    consumption_bills: CategorizedFileInfo[];
    floor_plans: CategorizedFileInfo[];
  };
}

/**
 * Get German display names for field names
 */
const getFieldDisplayName = (fieldName: string): string => {
  const displayNames: Record<string, string> = {
    'gebaeudebild': 'Gebäudebild',
    'verbrauchsrechnung1': 'Verbrauchsrechnung 1',
    'verbrauchsrechnung2': 'Verbrauchsrechnung 2',
    'verbrauchsrechnung3': 'Verbrauchsrechnung 3',
    'grundriss': 'Grundriss',
    'unknown': 'Unbekannt'
  };
  return displayNames[fieldName] || fieldName;
};

/**
 * Categorize a file based on its field name
 */
const categorizeFile = (file: StorageFileInfo): FileCategory => {
  if (file.fieldName === 'gebaeudebild') {
    return 'building_images';
  } else if (file.fieldName.startsWith('verbrauchsrechnung')) {
    return 'consumption_bills';
  } else if (file.fieldName === 'grundriss') {
    return 'floor_plans';
  }
  // Default to consumption bills for unknown types
  return 'consumption_bills';
};

/**
 * Get all files for a certificate with categorization (admin version)
 * This function can access files for any user/certificate combination
 */
export const getAdminCertificateFiles = async (
  userId: string,
  certificateId: string
): Promise<CertificateFileStats> => {
  try {
    // Get all files for this certificate
    const files = await listCertificateFiles(userId, certificateId);
    
    // Categorize and enhance file information
    const categorizedFiles: CategorizedFileInfo[] = files.map(file => ({
      ...file,
      category: categorizeFile(file),
      displayName: getFieldDisplayName(file.fieldName)
    }));

    // Group files by category
    const buildingImages = categorizedFiles.filter(f => f.category === 'building_images');
    const consumptionBills = categorizedFiles.filter(f => f.category === 'consumption_bills');
    const floorPlans = categorizedFiles.filter(f => f.category === 'floor_plans');

    // Calculate statistics
    const totalSize = categorizedFiles.reduce((sum, file) => sum + file.size, 0);

    return {
      totalFiles: categorizedFiles.length,
      totalSize,
      buildingImages: buildingImages.length,
      consumptionBills: consumptionBills.length,
      floorPlans: floorPlans.length,
      categories: {
        building_images: buildingImages,
        consumption_bills: consumptionBills,
        floor_plans: floorPlans
      }
    };
  } catch (error) {
    console.error('Error getting admin certificate files:', error);
    return {
      totalFiles: 0,
      totalSize: 0,
      buildingImages: 0,
      consumptionBills: 0,
      floorPlans: 0,
      categories: {
        building_images: [],
        consumption_bills: [],
        floor_plans: []
      }
    };
  }
};

/**
 * Format file size in human-readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

/**
 * Format upload date in German format
 */
export const formatUploadDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return 'Unbekannt';
  }
};

/**
 * Get signed URLs for multiple files
 */
export const getMultipleSignedUrls = async (filePaths: string[]): Promise<Record<string, string | null>> => {
  const urls: Record<string, string | null> = {};
  
  // Process files in parallel but limit concurrency
  const batchSize = 5;
  for (let i = 0; i < filePaths.length; i += batchSize) {
    const batch = filePaths.slice(i, i + batchSize);
    const batchPromises = batch.map(async (path) => {
      const url = await getSignedUrl(path);
      return { path, url };
    });
    
    const batchResults = await Promise.all(batchPromises);
    batchResults.forEach(({ path, url }) => {
      urls[path] = url;
    });
  }
  
  return urls;
};

/**
 * Download a file as blob for bulk download
 */
export const downloadFileAsBlob = async (filePath: string): Promise<{ blob: Blob; filename: string } | null> => {
  try {
    const { data, error } = await supabase.storage
      .from('certificateuploads')
      .download(filePath);

    if (error || !data) {
      console.error('Error downloading file:', error);
      return null;
    }

    // Extract filename from path
    const filename = filePath.split('/').pop() || 'unknown_file';
    
    return { blob: data, filename };
  } catch (error) {
    console.error('Error downloading file as blob:', error);
    return null;
  }
};

/**
 * Get category display name in German
 */
export const getCategoryDisplayName = (category: FileCategory): string => {
  const displayNames: Record<FileCategory, string> = {
    'building_images': 'Gebäudebilder',
    'consumption_bills': 'Verbrauchsrechnungen',
    'floor_plans': 'Grundrisse'
  };
  return displayNames[category];
};

/**
 * Create and download a ZIP file containing all files for a certificate
 */
export const downloadCertificateFilesAsZip = async (
  userId: string,
  certificateId: string,
  orderNumber?: string | null
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Get file statistics
    const fileStats = await getAdminCertificateFiles(userId, certificateId);

    if (fileStats.totalFiles === 0) {
      return { success: false, error: 'Keine Dateien zum Download verfügbar' };
    }

    // Create ZIP instance
    const zip = new JSZip();

    // Collect all files
    const allFiles = [
      ...fileStats.categories.building_images,
      ...fileStats.categories.consumption_bills
    ];

    // Download and add files to ZIP
    let successCount = 0;
    let errorCount = 0;

    for (const file of allFiles) {
      try {
        const fileData = await downloadFileAsBlob(file.path);
        if (fileData) {
          // Create folder structure in ZIP
          const folderName = getCategoryDisplayName(file.category);
          const fileName = `${file.displayName}_${file.originalName}`;
          zip.folder(folderName)?.file(fileName, fileData.blob);
          successCount++;
        } else {
          errorCount++;
          console.warn(`Failed to download file: ${file.path}`);
        }
      } catch (error) {
        errorCount++;
        console.error(`Error processing file ${file.path}:`, error);
      }
    }

    if (successCount === 0) {
      return { success: false, error: 'Keine Dateien konnten heruntergeladen werden' };
    }

    // Generate ZIP file
    const zipBlob = await zip.generateAsync({ type: 'blob' });

    // Create download link
    const url = URL.createObjectURL(zipBlob);
    const link = document.createElement('a');
    link.href = url;

    // Create filename
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = orderNumber
      ? `Energieausweis_${orderNumber}_Dateien_${timestamp}.zip`
      : `Energieausweis_${certificateId.slice(0, 8)}_Dateien_${timestamp}.zip`;

    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up
    URL.revokeObjectURL(url);

    const message = errorCount > 0
      ? `${successCount} von ${allFiles.length} Dateien erfolgreich heruntergeladen`
      : `Alle ${successCount} Dateien erfolgreich heruntergeladen`;

    return { success: true, error: errorCount > 0 ? message : undefined };
  } catch (error) {
    console.error('Error creating ZIP download:', error);
    return {
      success: false,
      error: `Fehler beim Erstellen des ZIP-Downloads: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`
    };
  }
};
