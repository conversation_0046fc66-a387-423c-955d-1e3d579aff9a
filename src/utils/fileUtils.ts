import { supabase } from '../lib/supabase';

/**
 * Interface for file information returned by directory listing
 */
export interface StorageFileInfo {
  name: string;
  path: string;
  size: number;
  created_at: string;
  updated_at: string;
  fieldName: string; // extracted from filename (verbrauchsrechnung1, verbrauchsrechnung2, geb<PERSON><PERSON>bild, grundriss, etc.)
  originalName: string; // filename without field prefix
  fileType: 'pdf' | 'image' | 'other';
}

/**
 * Lists all files in a certificate directory
 * @param userId The user ID
 * @param certificateId The certificate ID
 * @returns Promise<StorageFileInfo[]> Array of file information
 */
export const listCertificateFiles = async (userId: string, certificateId: string): Promise<StorageFileInfo[]> => {
  try {
    if (!userId || !certificateId) {
      console.error('Invalid userId or certificateId provided');
      return [];
    }

    const directoryPath = `${userId}/${certificateId}`;

    const { data, error } = await supabase.storage
      .from('certificateuploads')
      .list(directoryPath, {
        limit: 100,
        sortBy: { column: 'name', order: 'asc' }
      });

    if (error) {
      console.error('Error listing certificate files:', error);
      return [];
    }

    if (!data) {
      return [];
    }

    // Process files and extract metadata
    const fileInfos: StorageFileInfo[] = data
      .filter(item => !item.name.endsWith('/')) // Filter out directories
      .map(file => {
        const fullPath = `${directoryPath}/${file.name}`;

        // Extract field name and original filename
        const { fieldName, originalName } = extractFileMetadata(file.name);

        // Determine file type
        const fileType = getFileTypeFromName(originalName);

        return {
          name: file.name,
          path: fullPath,
          size: file.metadata?.size || 0,
          created_at: file.created_at || '',
          updated_at: file.updated_at || '',
          fieldName,
          originalName,
          fileType
        };
      });

    return fileInfos;
  } catch (error) {
    console.error('Error in listCertificateFiles:', error);
    return [];
  }
};

/**
 * Extracts field name and original filename from storage filename
 * @param fileName The storage filename (e.g., "verbrauchsrechnung1_document.pdf", "grundriss_floorplan.pdf")
 * @returns Object with fieldName and originalName
 */
export const extractFileMetadata = (fileName: string): { fieldName: string; originalName: string } => {
  // Match pattern: fieldName_originalName
  const match = fileName.match(/^(verbrauchsrechnung[123]|gebaeudebild|grundriss|typenschild)_(.+)$/);

  if (match) {
    return {
      fieldName: match[1],
      originalName: match[2]
    };
  }

  // Fallback for files that don't match the expected pattern
  return {
    fieldName: 'unknown',
    originalName: fileName
  };
};

/**
 * Determines file type from filename
 * @param fileName The filename
 * @returns File type category
 */
export const getFileTypeFromName = (fileName: string): 'pdf' | 'image' | 'other' => {
  const name = fileName.toLowerCase();

  if (name.endsWith('.pdf')) {
    return 'pdf';
  } else if (name.match(/\.(jpg|jpeg|png|webp|gif)$/)) {
    return 'image';
  }

  return 'other';
};

/**
 * Deletes a file from the certificateuploads storage bucket
 * @param filePath The file path to delete
 * @returns Promise<boolean> indicating success or failure
 */
export const deleteFile = async (filePath: string): Promise<boolean> => {
  try {
    if (!filePath || filePath.trim() === '') {
      console.error('Invalid file path provided for deletion');
      return false;
    }

    const { error } = await supabase.storage
      .from('certificateuploads')
      .remove([filePath]);

    if (error) {
      console.error('Error deleting file from storage:', error);
      return false;
    }

    console.log('Successfully deleted file:', filePath);
    return true;
  } catch (error) {
    console.error('Error in deleteFile:', error);
    return false;
  }
};

/**
 * Transfers all files from one user directory to another for a specific certificate
 * Used during account conversion to move files from anonymous user to registered user
 * Uses Edge Function with service role permissions to bypass RLS policies
 * @param fromUserId The source user ID (anonymous user)
 * @param toUserId The target user ID (registered user)
 * @param certificateId The certificate ID
 * @returns Promise<{success: boolean, error?: string, transferredFiles?: number}>
 */
export const transferCertificateFiles = async (
  fromUserId: string,
  toUserId: string,
  certificateId: string
): Promise<{
  success: boolean;
  error?: string;
  transferredFiles?: number;
  failedFiles?: string[];
}> => {
  try {
    console.log(`🔄 Starting file transfer via Edge Function: ${fromUserId}/${certificateId} -> ${toUserId}/${certificateId}`);

    // Check if source and target are the same
    if (fromUserId === toUserId) {
      console.log('ℹ️ Source and target users are the same, no transfer needed');
      return { success: true, transferredFiles: 0 };
    }

    // Call the Edge Function to handle file transfer with service role permissions
    const { data, error } = await supabase.functions.invoke('transfer-certificate-files', {
      body: {
        sourceUserId: fromUserId,
        targetUserId: toUserId,
        certificateId: certificateId
      }
    });

    if (error) {
      console.error('❌ Edge Function error:', error);
      return {
        success: false,
        error: `Fehler beim Aufrufen der Übertragungsfunktion: ${error.message}`
      };
    }

    if (!data) {
      console.error('❌ No response data from Edge Function');
      return {
        success: false,
        error: 'Keine Antwort von der Übertragungsfunktion erhalten'
      };
    }

    console.log('📊 Edge Function response:', data);

    // Return the response from the Edge Function
    return {
      success: data.success,
      transferredFiles: data.transferredFiles,
      failedFiles: data.failedFiles,
      error: data.error
    };

  } catch (error: any) {
    console.error('❌ Unexpected error during file transfer:', error);
    return {
      success: false,
      error: error.message || 'Unbekannter Fehler beim Übertragen der Dateien'
    };
  }
};



/**
 * Copies a file from one location to another within the same storage bucket
 * @param sourcePath The source file path
 * @param targetPath The target file path
 * @returns Promise<{success: boolean, error?: string}>
 */
export const copyFile = async (
  sourcePath: string,
  targetPath: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Download the file from source
    const { data: fileData, error: downloadError } = await supabase.storage
      .from('certificateuploads')
      .download(sourcePath);

    if (downloadError || !fileData) {
      return {
        success: false,
        error: `Failed to download source file: ${downloadError?.message || 'No data received'}`
      };
    }

    // Upload the file to target location
    const { error: uploadError } = await supabase.storage
      .from('certificateuploads')
      .upload(targetPath, fileData, {
        upsert: false // Don't overwrite existing files
      });

    if (uploadError) {
      return {
        success: false,
        error: `Failed to upload to target location: ${uploadError.message}`
      };
    }

    return { success: true };

  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Unexpected error during file copy'
    };
  }
};

/**
 * Gets a signed URL for a file in the consolidated certificateuploads bucket
 * Handles both direct file paths and full URLs (including public URLs)
 *
 * @param path The file path or full URL
 * @returns A signed URL that can be used to access the file
 */
export const getSignedUrl = async (path: string): Promise<string | null> => {
  try {
    let filePath: string = '';
    const bucket = 'certificateuploads';

    // Check if path is a full URL or just a file path
    if (path.startsWith('http')) {
      // Extract the file path from the full URL
      // Expected URL format: https://[project].supabase.co/storage/v1/object/public/[bucket]/[file-path]
      const urlParts = path.split('/');

      // Find the 'public' segment first, then look for the bucket after it
      const publicIndex = urlParts.findIndex(part => part === 'public');

      if (publicIndex !== -1 && urlParts[publicIndex + 1] === bucket) {
        // Extract path after bucket in public URL
        // Everything after the bucket name is the file path
        filePath = urlParts.slice(publicIndex + 2).join('/');
      } else {
        // Try to find the bucket after 'object/sign/' for signed URLs
        const signIndex = urlParts.findIndex(part => part === 'sign');

        if (signIndex !== -1 && urlParts[signIndex + 1] === bucket) {
          // Extract path after bucket in signed URL
          filePath = urlParts.slice(signIndex + 2).join('/');
        } else {
          // Try to find the bucket directly in the URL
          const bucketIndex = urlParts.findIndex(part => part === bucket);
          if (bucketIndex !== -1) {
            // Extract the file path (everything after the bucket name)
            filePath = urlParts.slice(bucketIndex + 1).join('/');
          } else {
            console.error(`Invalid file path format or bucket not found in path:`, path);
            return null;
          }
        }
      }

      // Validate that we have a non-empty file path
      if (!filePath || filePath.trim() === '') {
        console.error('Extracted file path is empty for URL:', path);
        return null;
      }
    } else {
      // Path is already a file path
      filePath = path;
    }

    // Get a signed URL that will work for 60 minutes (3600 seconds)
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(filePath, 3600);

    if (error) {
      console.error('Error creating signed URL:', error);
      return null;
    }

    return data.signedUrl;
  } catch (error) {
    console.error('Error in getSignedUrl:', error);
    return null;
  }
};