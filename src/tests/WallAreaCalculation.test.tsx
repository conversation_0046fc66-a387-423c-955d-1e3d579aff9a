import { describe, it, expect } from "vitest";

/**
 * Test suite for Wall Area Calculation functionality
 *
 * This test verifies the mathematical calculation used in the WallAreaCalculator component:
 * Total Area = Width × Number of Levels × Room Height
 * Where Room Height comes from gebaeudedetails1.Raumhöhe (dynamic value from user input)
 */
describe("Wall Area Calculation", () => {
  it("should calculate wall area correctly with basic values", () => {
    const width = 10; // meters
    const levels = 2; // number of levels
    const roomHeight = 2.8; // meters from gebaeudedetails1.Raumhöhe

    const expectedArea = width * levels * roomHeight;
    const calculatedArea = 10 * 2 * 2.8;

    expect(calculatedArea).toBe(56);
    expect(calculatedArea).toBe(expectedArea);
  });

  it("should calculate wall area correctly with decimal values", () => {
    const width = 12.5; // meters
    const levels = 3; // number of levels
    const roomHeight = 2.65; // meters from gebaeudedetails1.<PERSON><PERSON><PERSON>öhe

    const expectedArea = width * levels * roomHeight;
    const calculatedArea = 12.5 * 3 * 2.65;

    expect(calculatedArea).toBe(99.375);
    expect(calculatedArea).toBe(expectedArea);
  });

  it("should handle single level correctly", () => {
    const width = 8; // meters
    const levels = 1; // number of levels
    const roomHeight = 3.0; // meters from gebaeudedetails1.Raumhöhe

    const expectedArea = width * levels * roomHeight;
    const calculatedArea = 8 * 1 * 3.0;

    expect(calculatedArea).toBe(24);
    expect(calculatedArea).toBe(expectedArea);
  });

  it("should format result to 2 decimal places when needed", () => {
    const width = 7.3; // meters
    const levels = 2; // number of levels
    const roomHeight = 2.75; // meters from gebaeudedetails1.Raumhöhe

    const calculatedArea = width * levels * roomHeight;
    const formattedArea = parseFloat(calculatedArea.toFixed(2));

    expect(formattedArea).toBe(40.15);
  });

  it("should handle German decimal format conversion", () => {
    // Simulate German input format (comma as decimal separator)
    const germanWidthInput = "12,5";
    const germanRoomHeightInput = "2,8"; // from gebaeudedetails1.Raumhöhe
    const width = parseFloat(germanWidthInput.replace(",", "."));
    const roomHeight = parseFloat(germanRoomHeightInput.replace(",", "."));
    const levels = 2;

    const calculatedArea = width * levels * roomHeight;
    const germanFormattedResult = calculatedArea.toFixed(2).replace(".", ",");

    expect(width).toBe(12.5);
    expect(roomHeight).toBe(2.8);
    expect(calculatedArea).toBe(70);
    expect(germanFormattedResult).toBe("70,00");
  });

  it("should return zero for invalid inputs", () => {
    const invalidWidth = NaN;
    const validLevels = 2;
    const roomHeight = 2.75; // from gebaeudedetails1.Raumhöhe

    const calculatedArea = invalidWidth * validLevels * roomHeight;

    expect(isNaN(calculatedArea)).toBe(true);
  });

  it("should return zero for zero or negative inputs", () => {
    const zeroWidth = 0;
    const negativeWidth = -5;
    const validLevels = 2;
    const roomHeight = 2.75; // from gebaeudedetails1.Raumhöhe

    const zeroResult = zeroWidth * validLevels * roomHeight;
    const negativeResult = negativeWidth * validLevels * roomHeight;

    expect(zeroResult).toBe(0);
    expect(negativeResult).toBe(-27.5);
  });
});
