import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider, useQuery } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import React from 'react';

// Mock Supabase
vi.mock('../lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn()
        }))
      }))
    }))
  }
}));

describe('GebaeudeformPage Data Query Pattern', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    vi.clearAllMocks();
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  it('should fetch both gebaeudedetails1 and gebaeudedetails2 data correctly', async () => {
    const mockData = {
      gebaeudedetails1: {
        Geschosse: '2',
        Raumhöhe: '2,8',
        Baujahr: '1990'
      },
      gebaeudedetails2: {
        buildingShape: 'rectangular',
        waende: []
      }
    };

    // Mock successful Supabase response
    const mockSingle = vi.fn().mockResolvedValue({
      data: mockData,
      error: null
    });

    const mockEq = vi.fn(() => ({ single: mockSingle }));
    const mockSelect = vi.fn(() => ({ eq: mockEq }));
    const mockFrom = vi.fn(() => ({ select: mockSelect }));

    vi.mocked(supabase.from).mockImplementation(mockFrom);

    // Test the exact query pattern used in GebaeudeformPage
    const useGebaeudeformQuery = (activeCertificateId: string) => {
      return useQuery({
        queryKey: ['energieausweise', 'gebaeudeform', activeCertificateId],
        queryFn: async () => {
          if (!activeCertificateId) return null;

          const { data, error } = await supabase
            .from('energieausweise')
            .select('gebaeudedetails2, gebaeudedetails1')
            .eq('id', activeCertificateId)
            .single();

          if (error) {
            throw error;
          }
          return data;
        },
        enabled: !!activeCertificateId,
        retry: false,
      });
    };

    const { result } = renderHook(
      () => useGebaeudeformQuery('test-certificate-id'),
      { wrapper }
    );

    // Wait for the query to complete
    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    // Verify that the query was called correctly
    expect(supabase.from).toHaveBeenCalledWith('energieausweise');
    expect(mockSelect).toHaveBeenCalledWith('gebaeudedetails2, gebaeudedetails1');
    expect(mockEq).toHaveBeenCalledWith('id', 'test-certificate-id');

    // Verify that both datasets are returned
    expect(result.current.data).toEqual(mockData);
    expect(result.current.data?.gebaeudedetails1).toBeDefined();
    expect(result.current.data?.gebaeudedetails2).toBeDefined();

    // Verify WallAreaCalculator required data is accessible
    expect(result.current.data?.gebaeudedetails1?.Geschosse).toBe('2');
    expect(result.current.data?.gebaeudedetails1?.Raumhöhe).toBe('2,8');
  });

  it('should handle missing gebaeudedetails1 gracefully', async () => {
    const mockData = {
      gebaeudedetails2: {
        buildingShape: 'L-shaped',
        waende: []
      }
      // gebaeudedetails1 is intentionally missing
    };

    const mockSingle = vi.fn().mockResolvedValue({
      data: mockData,
      error: null
    });

    const mockEq = vi.fn(() => ({ single: mockSingle }));
    const mockSelect = vi.fn(() => ({ eq: mockEq }));
    const mockFrom = vi.fn(() => ({ select: mockSelect }));

    vi.mocked(supabase.from).mockImplementation(mockFrom);

    const useGebaeudeformQuery = (activeCertificateId: string) => {
      return useQuery({
        queryKey: ['energieausweise', 'gebaeudeform', activeCertificateId],
        queryFn: async () => {
          if (!activeCertificateId) return null;

          const { data, error } = await supabase
            .from('energieausweise')
            .select('gebaeudedetails2, gebaeudedetails1')
            .eq('id', activeCertificateId)
            .single();

          if (error) {
            throw error;
          }
          return data;
        },
        enabled: !!activeCertificateId,
        retry: false,
      });
    };

    const { result } = renderHook(
      () => useGebaeudeformQuery('test-certificate-id'),
      { wrapper }
    );

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    // Verify that the query still works even without gebaeudedetails1
    expect(result.current.data).toEqual(mockData);
    expect(result.current.data?.gebaeudedetails2).toBeDefined();
    expect(result.current.data?.gebaeudedetails1).toBeUndefined();

    // WallAreaCalculator should handle this case with fallbacks:
    // geschosseValue = gebaeudedetails1Data?.Geschosse || '';
    // raumhoeheValue = gebaeudedetails1Data?.Raumhöhe || '';
  });

  it('should match the pattern used in GebaeudedetailsPage2', async () => {
    // This test verifies we're using the same data fetching pattern
    const mockData = {
      gebaeudedetails1: {
        Geschosse: '3',
        Raumhöhe: '2,7',
        Baujahr: '1985'
      },
      gebaeudedetails2: {
        buildingShape: 'rectangular',
        waende: [
          {
            id: 'wall-1',
            bezeichnung: 'Nordwand',
            massiv: 'kb_Massiv',
            flaeche: '25,5'
          }
        ]
      }
    };

    const mockSingle = vi.fn().mockResolvedValue({
      data: mockData,
      error: null
    });

    const mockEq = vi.fn(() => ({ single: mockSingle }));
    const mockSelect = vi.fn(() => ({ eq: mockEq }));
    const mockFrom = vi.fn(() => ({ select: mockSelect }));

    vi.mocked(supabase.from).mockImplementation(mockFrom);

    const useGebaeudeformQuery = (activeCertificateId: string) => {
      return useQuery({
        queryKey: ['energieausweise', 'gebaeudeform', activeCertificateId],
        queryFn: async () => {
          if (!activeCertificateId) return null;

          const { data, error } = await supabase
            .from('energieausweise')
            .select('gebaeudedetails2, gebaeudedetails1')
            .eq('id', activeCertificateId)
            .single();

          if (error) {
            throw error;
          }
          return data;
        },
        enabled: !!activeCertificateId,
        retry: false,
      });
    };

    const { result } = renderHook(
      () => useGebaeudeformQuery('test-certificate-id'),
      { wrapper }
    );

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    // Verify the query follows the same pattern as GebaeudedetailsPage2
    // GebaeudedetailsPage2 fetches: 'gebaeudedetails2, gebaeudedetails1, certificate_type, heizung, trinkwarmwasser'
    // GebaeudeformPage fetches: 'gebaeudedetails2, gebaeudedetails1' (sufficient for WallAreaCalculator needs)

    expect(mockSelect).toHaveBeenCalledWith('gebaeudedetails2, gebaeudedetails1');

    // Verify that the returned data structure allows WallAreaCalculator to work
    const existingData = result.current.data;
    const gebaeudedetails1Data = existingData?.gebaeudedetails1 as any;
    const geschosseValue = gebaeudedetails1Data?.Geschosse || '';
    const raumhoeheValue = gebaeudedetails1Data?.Raumhöhe || '';

    expect(geschosseValue).toBe('3');
    expect(raumhoeheValue).toBe('2,7');

    // Simulate wall area calculation
    const wallWidth = '12,5'; // User input
    const width = parseFloat(wallWidth.replace(',', '.'));
    const levels = parseFloat(geschosseValue);
    const roomHeight = parseFloat(raumhoeheValue.replace(',', '.'));

    if (!isNaN(width) && !isNaN(levels) && !isNaN(roomHeight) && width > 0 && levels > 0 && roomHeight > 0) {
      const area = width * levels * roomHeight;
      const formattedArea = area.toFixed(2).replace('.', ',');

      // This should work: 12.5 * 3 * 2.7 = 101.25
      expect(area).toBe(101.25);
      expect(formattedArea).toBe('101,25');
    }
  });

  it('should handle database errors properly', async () => {
    const mockError = new Error('Database connection failed');

    const mockSingle = vi.fn().mockResolvedValue({
      data: null,
      error: mockError
    });

    const mockEq = vi.fn(() => ({ single: mockSingle }));
    const mockSelect = vi.fn(() => ({ eq: mockEq }));
    const mockFrom = vi.fn(() => ({ select: mockSelect }));

    vi.mocked(supabase.from).mockImplementation(mockFrom);

    const useGebaeudeformQuery = (activeCertificateId: string) => {
      return useQuery({
        queryKey: ['energieausweise', 'gebaeudeform', activeCertificateId],
        queryFn: async () => {
          if (!activeCertificateId) return null;

          const { data, error } = await supabase
            .from('energieausweise')
            .select('gebaeudedetails2, gebaeudedetails1')
            .eq('id', activeCertificateId)
            .single();

          if (error) {
            throw error;
          }
          return data;
        },
        enabled: !!activeCertificateId,
        retry: false,
      });
    };

    const { result } = renderHook(
      () => useGebaeudeformQuery('test-certificate-id'),
      { wrapper }
    );

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    // Verify that the query was attempted with the correct parameters
    expect(mockSelect).toHaveBeenCalledWith('gebaeudedetails2, gebaeudedetails1');
    expect(result.current.error).toEqual(mockError);
  });
});
