import React from 'react';

/**
 * Skeleton used while certificate data is being initialized.
 * Matches the layout of the Zusammenfassung summary page and is
 * intentionally simple and accessible.
 */
const CertificateLoadingSkeleton: React.FC = () => (
  <div className="min-h-screen bg-gray-50 py-8" role="status" aria-live="polite">
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <div className="animate-pulse space-y-8">
        {/* Header skeleton */}
        <div className="h-8 bg-gray-200 rounded w-1/3" aria-hidden="true"></div>

        {/* Short description skeleton */}
        <div className="h-6 bg-gray-200 rounded w-1/2" aria-hidden="true"></div>

        {/* Content sections skeleton */}
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white shadow rounded-lg p-6">
            <div className="h-6 bg-gray-200 rounded w-1/4 mb-4" aria-hidden="true"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded w-full" aria-hidden="true"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4" aria-hidden="true"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

export default CertificateLoadingSkeleton;
