import React from 'react';
import { useLegalConsent } from '../../hooks/useLegalConsent';

/**
 * Legal consent checkboxes and validation UI component
 * Consumes the useLegalConsent hook internally (context-only)
 */
export const LegalConsentSection: React.FC = () => {
  const { legalConsent, updateConsent, isValid, getValidationMessage } = useLegalConsent();

  console.log('🚀 LegalConsentSection loaded');

  const validationMessage = getValidationMessage();

  return (
    <div className="bg-white shadow rounded-lg p-6 mb-8">
      <h3 className="text-lg font-medium text-gray-900 mb-6">
        Rechtliche Bestimmungen
      </h3>

      {/* Validation Error Message */}
      {!isValid && validationMessage && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{validationMessage}</p>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-4">
        {/* AGB Checkbox */}
        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="agb"
              type="checkbox"
              checked={legalConsent.agb}
              onChange={(e) => updateConsent('agb', e.target.checked)}
              className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
            />
          </div>
          <div className="ml-3 text-sm">
            <label htmlFor="agb" className="text-gray-700">
              Ich stimme den{' '}
              <a
                href="/legal/agb"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-500 underline"
              >
                Allgemeinen Geschäftsbedingungen
              </a>{' '}
              zu. <span className="text-red-500">*</span>
            </label>
          </div>
        </div>

        {/* Datenschutz Checkbox */}
        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="datenschutz"
              type="checkbox"
              checked={legalConsent.datenschutz}
              onChange={(e) => updateConsent('datenschutz', e.target.checked)}
              className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
            />
          </div>
          <div className="ml-3 text-sm">
            <label htmlFor="datenschutz" className="text-gray-700">
              Ich stimme der{' '}
              <a
                href="/legal/datenschutz"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-500 underline"
              >
                Datenschutzerklärung
              </a>{' '}
              zu. <span className="text-red-500">*</span>
            </label>
          </div>
        </div>

        {/* Widerruf Checkbox */}
        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="widerruf"
              type="checkbox"
              checked={legalConsent.widerruf}
              onChange={(e) => updateConsent('widerruf', e.target.checked)}
              className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
            />
          </div>
          <div className="ml-3 text-sm">
            <label htmlFor="widerruf" className="text-gray-700">
              Ich habe die{' '}
              <a
                href="/legal/widerruf"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-500 underline"
              >
                Widerrufsbelehrung
              </a>{' '}
              zur Kenntnis genommen. <span className="text-red-500">*</span>
            </label>
          </div>
        </div>

        {/* Data Accuracy Checkbox */}
        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="dataAccuracy"
              type="checkbox"
              checked={legalConsent.dataAccuracy}
              onChange={(e) => updateConsent('dataAccuracy', e.target.checked)}
              className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
            />
          </div>
          <div className="ml-3 text-sm">
            <label htmlFor="dataAccuracy" className="text-gray-700">
              Ich bestätige, dass alle von mir angegebenen Daten korrekt und vollständig sind. <span className="text-red-500">*</span>
            </label>
          </div>
        </div>
      </div>

      <div className="mt-4 text-xs text-gray-500">
        <span className="text-red-500">*</span> Pflichtfelder
      </div>
    </div>
  );
};