import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Breadcrumb } from '../ui/Breadcrumb';

export interface SummaryHeaderProps {
  title?: string;
  showAnonymousIndicator?: boolean;
}

/**
 * Header component for the summary page
 * Displays title, breadcrumb navigation, and anonymous user indicator
 */
export const SummaryHeader: React.FC<SummaryHeaderProps> = ({
  title = 'Zusammenfassung',
  showAnonymousIndicator = true
}) => {
  const { isAnonymous } = useAuth();

  console.log('SummaryHeader render'); 

  return (
    <div className="mb-8">
      {/* Breadcrumb Navigation */}
      <Breadcrumb />

      {/* Page Title */}
      <h1 className="text-3xl font-bold text-gray-900 mb-4">
        {title}
      </h1>

      {/* Anonymous User Indicator */}
      {showAnonymousIndicator && isAnonymous && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Gastzugang
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  Sie nutzen derzeit den Gastzugang. Um Ihren Energieausweis zu kaufen,
                  werden Sie aufgefordert, ein Konto zu erstellen oder sich anzumelden.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};