import React from 'react';

export interface PricingOverviewProps {
  pricingInfo: any;
  certificateType: string | null | undefined;
  loading?: boolean;
}

/**
 * Pricing table and certificate selection display component
 * Shows pricing information and selected certificate type
 */
export const PricingOverview: React.FC<PricingOverviewProps> = ({
  pricingInfo,
  certificateType,
  loading = false
}) => {

  console.log('Pricing Overview', pricingInfo);
  
  if (loading) {
    return (
      <div className="bg-white shadow rounded-lg p-6 mb-8">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Preisübersicht
        </h3>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (!pricingInfo) {
    return null;
  }

  const getCertificateTypeLabel = (type: string | null | undefined) => {
    switch (type) {
      case 'WG/V':
        return 'Wohngebäude-Verbrauchsausweis';
      case 'WG/B':
        return 'Wohngebäude-Bedarfsausweis';
      case 'NWG/V':
        return 'Nicht-Wohngebäude-Verbrauchsausweis';
      default:
        return 'Nicht angegeben';
    }
  };

  return (
    <div className="bg-white shadow rounded-lg p-6 mb-8">
      <h3 className="text-lg font-medium text-gray-900 mb-6">
        Preisübersicht
      </h3>

      {/* Pricing Table */}
      <div className="overflow-hidden">
        <table className="min-w-full table-fixed divide-y divide-gray-200">
          <colgroup>
            <col style={{ width: '60%' }} />
            <col style={{ width: '40%' }} />
          </colgroup>
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Leistung
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Preis
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            <tr className="bg-green-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <div className="text-sm font-medium text-gray-900">
                      {pricingInfo.name || getCertificateTypeLabel(certificateType)}
                    </div>
                    {pricingInfo.description && (
                      <div className="text-sm text-gray-500">
                        {pricingInfo.description}
                      </div>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right">
                <div className="text-lg font-bold text-gray-900">
                  {pricingInfo.price || '49,00 €'}
                </div>
                <div className="text-sm text-gray-500">
                  inkl. MwSt.
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Additional Information */}
      {pricingInfo.features && pricingInfo.features.length > 0 && (
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-900 mb-3">
            Enthaltene Leistungen:
          </h4>
          <ul className="space-y-2">
            {pricingInfo.features.map((feature: string, index: number) => (
              <li key={index} className="flex items-start">
                <svg className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-sm text-gray-700">{feature}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};