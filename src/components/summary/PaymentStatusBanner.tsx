import React from 'react';
import { useCertificate } from '../../contexts/CertificateContext';
/**
 * Status-specific banners for payment states
 * Displays appropriate messages for paid, in progress, failed, etc.
 */
export const PaymentStatusBanner: React.FC = () => {
  const ctx = useCertificate();
  const status = ctx.certificateData?.status;
  const orderNumber = ctx.certificateData?.order_number || (ctx.certificateData?.id ? `EA-${ctx.certificateData.id.slice(-8).toUpperCase()}` : undefined);

  console.log('PaymentStatusBanner render:', status);
  
  // Don't render anything if no status or not a payment status
  if (!status || !['payment_initiated', 'payment_complete', 'payment_failed', 'payment_disputed', 'payment_expired'].includes(status)) {
    return null;
  }

  // Payment completed successfully
  if (status === 'payment_complete') {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-lg font-medium text-green-800">
              Zahlung erfolgreich abgeschlossen
            </h3>
            <div className="mt-2 text-sm text-green-700">
              <p>
                Vielen Dank für Ihren Kauf! Ihr Energieausweis wurde erfolgreich bezahlt.
                {orderNumber && (
                  <span className="block mt-1 font-medium">
                    Bestellnummer: {orderNumber}
                  </span>
                )}
              </p>
              <p className="mt-2">
                Sie erhalten in Kürze eine Bestätigungs-E-Mail mit weiteren Informationen.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Payment in progress
  if (status === 'payment_initiated') {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-lg font-medium text-yellow-800">
              Zahlung wird verarbeitet
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>
                Ihre Zahlung wird derzeit verarbeitet. Dies kann einige Minuten dauern.
              </p>
              <p className="mt-2">
                Bitte schließen Sie diese Seite nicht und warten Sie auf die Bestätigung.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Payment failed, disputed, or expired
  if (['payment_failed', 'payment_disputed', 'payment_expired'].includes(status)) {
    const getFailureMessage = () => {
      switch (status) {
        case 'payment_failed':
          return 'Die Zahlung konnte nicht verarbeitet werden.';
        case 'payment_disputed':
          return 'Die Zahlung wurde beanstandet.';
        case 'payment_expired':
          return 'Die Zahlungssitzung ist abgelaufen.';
        default:
          return 'Es gab ein Problem mit der Zahlung.';
      }
    };

    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-lg font-medium text-red-800">
              Zahlung fehlgeschlagen
            </h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{getFailureMessage()}</p>
              <p className="mt-2">
                Sie können die Zahlung erneut versuchen oder sich bei Problemen an unseren Support wenden.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};