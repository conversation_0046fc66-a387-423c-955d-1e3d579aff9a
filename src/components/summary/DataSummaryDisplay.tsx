import React from 'react';
import { DataSection } from '../ui/DataSection';
import { useCertificate } from '../../contexts/CertificateContext';
import { useSummaryData } from '../../hooks/useSummaryData';

/**
 * Main data display component using consolidated context
 * Internally derives sections and files for the active certificate
 */
export const DataSummaryDisplay: React.FC = () => {
  const { activeCertificateId, certificateFiles } = useCertificate();
  const { sections } = useSummaryData(activeCertificateId);

  console.log('Rendering DataSummaryDisplay');

  return (
    <div className="space-y-8 mb-8">
      {/* Render all data sections */}
      {sections?.map((section, index) => (
        <DataSection
          key={`${section.title}-${index}`}
          title={section.title}
          data={section.data}
          fields={section.fields}
          excludeFields={section.excludeFields}
          filesByField={certificateFiles}
        />
      ))}

    </div>
  );
};