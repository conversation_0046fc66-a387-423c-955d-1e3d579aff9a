import React from 'react';
import { DataSection } from '../ui/DataSection';
import { useCertificate } from '../../contexts/CertificateContext';
import { useSummaryData } from '../../hooks/useSummaryData';

/**
 * DataSummaryDisplay now assumes the provider has initialized data.
 * It renders nothing if there is no active certificate or sections.
 */
export const DataSummaryDisplay: React.FC = () => {
  const { activeCertificateId, certificateFiles } = useCertificate();
  const { sections } = useSummaryData(activeCertificateId);

  console.log('Rendering DataSummaryDisplay');

  if (!activeCertificateId || !sections || sections.length === 0) return null;

  return (
    <div className="space-y-8 mb-8">
      {sections.map((section, index) => (
        <DataSection
          key={`${section.title}-${index}`}
          title={section.title}
          data={section.data}
          fields={section.fields}
          excludeFields={section.excludeFields}
          filesByField={certificateFiles}
        />
      ))}
    </div>
  );
};