import React from 'react';
import { DataSection } from '../ui/DataSection';
import { type SectionConfig } from '../../hooks/useSummaryData';

export interface DataSummaryDisplayProps {
  sections: SectionConfig[];
  filesByField?: Record<string, any[]>;
  activeCertificateId?: string | null;
}

/**
 * Main data display component using existing DataSection components
 * Renders all data sections and handles file display components
 */
export const DataSummaryDisplay: React.FC<DataSummaryDisplayProps> = ({
  sections,
  filesByField = {}
}) => {
  
  console.log('Rendering DataSummaryDisplay');

  return (
    <div className="space-y-8 mb-8">
      {/* Render all data sections */}
      {sections.map((section, index) => (
        <DataSection
          key={`${section.title}-${index}`}
          title={section.title}
          data={section.data}
          fields={section.fields}
          excludeFields={section.excludeFields}
          filesByField={filesByField}
        />
      ))}

    </div>
  );
};