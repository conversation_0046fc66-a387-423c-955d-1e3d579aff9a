import React from 'react';
import { type LegalConsentState } from '../../hooks/useLegalConsent';

export interface SummaryActionsProps {
  // Payment flow props
  onCheckout: (legalConsent: LegalConsentState, isAnonymous: boolean, onShowAccountConversion: () => void) => Promise<void>;
  onBack: () => void;
  isProcessingPayment: boolean;
  error: string | null;

  // Legal consent props
  legalConsent: LegalConsentState;
  isValid: boolean;

  // Payment status props
  paymentStatus?: string | null;
  isPaid?: boolean;
  isPaymentFailed?: boolean;

  // Anonymous user props
  isAnonymous?: boolean;
  onShowAccountConversion?: () => void;
}

/**
 * Action buttons component for back navigation and checkout/payment
 * Handles conditional rendering based on payment status and user state
 */
export const SummaryActions: React.FC<SummaryActionsProps> = ({
  onCheckout,
  onBack,
  isProcessingPayment,
  error,
  legalConsent,
  isValid,
  isPaid = false,
  isPaymentFailed = false,
  isAnonymous = false,
  onShowAccountConversion = () => {}
}) => {
  console.log('SummaryActions render:', isPaid, isPaymentFailed, isAnonymous);
  
  // Don't show checkout button if already paid
  const showCheckoutButton = !isPaid;

  // Determine button text based on payment status
  const getCheckoutButtonText = () => {
    if (isProcessingPayment) {
      return 'Zahlung wird verarbeitet...';
    }

    if (isPaymentFailed) {
      return 'Zahlung erneut versuchen';
    }

    if (isAnonymous) {
      return 'Konto erstellen und bezahlen';
    }

    return 'Jetzt bezahlen';
  };

  // Handle checkout button click
  const handleCheckoutClick = async () => {
    await onCheckout(legalConsent, isAnonymous, onShowAccountConversion);
  };

  return (
    <div className="bg-white shadow rounded-lg p-6">
      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        {/* Back Button */}
        <button
          type="button"
          onClick={onBack}
          disabled={isProcessingPayment}
          className="inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Zurück
        </button>

        {/* Checkout/Payment Button */}
        {showCheckoutButton && (
          <button
            type="button"
            onClick={handleCheckoutClick}
            disabled={isProcessingPayment || !isValid}
            className={`inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed ${
              isValid && !isProcessingPayment
                ? 'bg-blue-600 hover:bg-blue-700'
                : 'bg-gray-400'
            }`}
          >
            {isProcessingPayment && (
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            {getCheckoutButtonText()}
          </button>
        )}

        {/* Success Message for Paid Certificates */}
        {isPaid && (
          <div className="inline-flex items-center px-6 py-3 bg-green-100 border border-green-300 rounded-md">
            <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span className="text-green-800 font-medium">
              Erfolgreich bezahlt
            </span>
          </div>
        )}
      </div>

      {/* Help Text - Only show if not paid and validation fails */}
      {!isPaid && !isValid && (
        <div className="mt-4 text-sm text-gray-600">
          <p>
            Bitte stimmen Sie allen rechtlichen Bestimmungen zu, um mit der Zahlung fortzufahren.
          </p>
        </div>
      )}
    </div>
  );
};