import React from 'react';

const CertificateOrderingInfo: React.FC = () => {
  return (

    <div className="bg-white shadow rounded-lg p-6 mb-8">
      <h3 className="text-lg font-medium text-gray-900 mb-6">
        Was passiert nach dem Kauf?
      </h3>
      <div className="bg-green-50 border-green-200 rounded-lg p-6 mb-6">
        <div className="space-y-3 text-green-800">
          <div className="flex items-start">
            <svg className="w-4 h-4 mr-3 mt-0.5 text-green-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>Sie erhalten eine Bestätigungs-E-Mail mit allen Details</span>
          </div>
          <div className="flex items-start">
            <svg className="w-4 h-4 mr-3 mt-0.5 text-green-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Ihr Energieausweis wird durch zertifizierte Energieberater innerhalb von 2-3 Werktagen erstellt</span>
          </div>
          <div className="flex items-start">
            <svg className="w-4 h-4 mr-3 mt-0.5 text-green-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <span>Sie werden per E-Mail benachrichtigt, sobald der Ausweis verfügbar ist</span>
          </div>
          <div className="flex items-start">
            <svg className="w-4 h-4 mr-3 mt-0.5 text-green-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span>Der fertige Ausweis wird Ihnen als PDF zugesendet</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CertificateOrderingInfo;