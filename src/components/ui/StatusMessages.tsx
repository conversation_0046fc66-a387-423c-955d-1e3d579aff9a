// Reusable error component for consistent UI
export const ErrorMessage = ({ 
  message, 
  onRetry,
  type = 'error'
}: { 
  message: string, 
  onRetry?: () => void,
  type?: 'error' | 'warning'
}) => {
  const colors = type === 'error' 
    ? {
        bg: 'bg-red-50',
        border: 'border-red-500',
        text: 'text-red-500',
        title: 'text-red-800',
        message: 'text-red-700',
        button: 'text-red-700'
      }
    : {
        bg: 'bg-yellow-50',
        border: 'border-yellow-400',
        text: 'text-yellow-400',
        title: 'text-yellow-800',
        message: 'text-yellow-700',
        button: 'text-yellow-700'
      };

  return (
    <div className={`${colors.bg} border-l-4 ${colors.border} p-4`}>
      <div className="flex">
        <div className="flex-shrink-0">
          <svg className={`h-5 w-5 ${colors.text}`} viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className={`text-sm font-medium ${colors.title}`}>
            {type === 'error' ? 'Fehler' : 'Warnung'}
          </h3>
          <p className={`text-sm ${colors.message} mt-1`}>{message}</p>
          {onRetry && (
            <button 
              onClick={onRetry}
              className={`mt-2 text-sm ${colors.button} underline`}
            >
              Erneut versuchen
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

// Reusable success message component
export const SuccessMessage = ({ message }: { message: string }) => (
  <div className="bg-green-50 border-l-4 border-green-500 p-4">
    <div className="flex">
      <div className="flex-shrink-0">
        <svg className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
        </svg>
      </div>
      <div className="ml-3">
        <h3 className="text-sm font-medium text-green-800">Erfolgreich</h3>
        <p className="text-sm text-green-700 mt-1">{message}</p>
      </div>
    </div>
  </div>
);

// Reusable loading component for consistent UI
export const LoadingSpinner = ({ message = 'Status: Wird geladen...' }: { message?: string }) => (
  <div className="text-center">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500 mx-auto mb-4"></div>
    <h2 className="text-xl font-semibold text-gray-900 mb-2">{message}</h2>
  </div>
);
