import { useState, useEffect } from 'react';
import { getSignedUrl } from '../../utils/fileUtils';

interface FileWithSignedUrlProps {
  path: string;
  label?: string;
  showLoading?: boolean;
  className?: string;
}

/**
 * Component to render a file with a signed URL
 * Handles files from the consolidated certificateuploads bucket
 */
export const FileWithSignedUrl = ({
  path,
  label = 'Datei anzeigen',
  showLoading = true,
  className = ''
}: FileWithSignedUrlProps) => {
  const [signedUrl, setSignedUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (path) {
      setIsLoading(true);
      setError(null);

      // Get signed URL
      getSignedUrl(path).then(url => {
        if (url) {
          setSignedUrl(url);
        } else {
          setError('Datei konnte nicht geladen werden');
        }
        setIsLoading(false);
      }).catch(err => {
        console.error('Error getting signed URL:', err);
        setError('Fehler beim Laden der Datei');
        setIsLoading(false);
      });
    }
  }, [path]);

  if (isLoading && showLoading) {
    return <span className="text-gray-400">Datei wird geladen...</span>;
  }

  if (error || !signedUrl) {
    return <span className="text-gray-400">{error || 'Datei nicht verfügbar'}</span>;
  }

  return (
    <div className={`flex items-center ${className}`}>
      <a
        href={signedUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="text-green-600 hover:underline"
      >
        {label}
      </a>
    </div>
  );
};