import React from 'react';

interface ValidationErrorSummaryProps {
  errors: Record<string, string[]>;
  title?: string;
  className?: string;
}

/**
 * Component to display a summary of validation errors
 * Useful for showing all errors at the top of a form
 */
export const ValidationErrorSummary: React.FC<ValidationErrorSummaryProps> = ({
  errors,
  title = 'Bitte korrigieren Sie folgende Fehler:',
  className = ''
}) => {
  const errorEntries = Object.entries(errors).filter(([_, errorList]) => errorList.length > 0);
  
  if (errorEntries.length === 0) {
    return null;
  }

  const totalErrors = errorEntries.reduce((sum, [_, errorList]) => sum + errorList.length, 0);

  return (
    <div className={`bg-red-50 border-l-4 border-red-500 p-4 mb-6 ${className}`} role="alert">
      <div className="flex">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800">
            {title}
          </h3>
          <div className="mt-2 text-sm text-red-700">
            <p className="mb-2">
              {totalErrors} Fehler {totalErrors > 1 ? 'wurden' : 'wurde'} gefunden:
            </p>
            <ul className="list-disc list-inside space-y-1">
              {errorEntries.map(([fieldName, errorList]) => (
                errorList.map((error, index) => (
                  <li key={`${fieldName}-${index}`}>
                    <strong>{getFieldDisplayName(fieldName)}:</strong> {error}
                  </li>
                ))
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Maps technical field names to user-friendly display names
 */
function getFieldDisplayName(fieldName: string): string {
  const fieldNameMappings: Record<string, string> = {
    // Objektdaten
    'Strasse': 'Straße',
    'Hausnr': 'Hausnummer',
    'PLZ': 'Postleitzahl',
    'Ort': 'Ort',
    'WSchVo77_erfuellt': 'Wärmeschutzverordnung 1977',
    
    // Kundendaten
    'Kunden_Anrede': 'Anrede',
    'Kunden_Vorname': 'Vorname',
    'Kunden_Nachname': 'Nachname',
    'Kunden_Strasse': 'Straße (Kunde)',
    'Kunden_Hausnr': 'Hausnummer (Kunde)',
    'Kunden_PLZ': 'Postleitzahl (Kunde)',
    'Kunden_Ort': 'Ort (Kunde)',
    'Kunden_email': 'E-Mail-Adresse',
    'Kunden_telefon': 'Telefonnummer',
    
    // Gebäudedetails
    'Baujahr': 'Baujahr',
    'Modernisierung': 'Modernisierung',
    'Wohnfläche': 'Wohnfläche',
    'Nutzung1_ID': 'Nutzung ID',
    'Nutzung1_Flaeche': 'Nutzungsfläche',
    'Raumhöhe': 'Raumhöhe',
    'Volumen': 'Gebäudevolumen',
    'Geschosse': 'Anzahl Geschosse',
    'anbauSituation': 'Anbausituation',
    'Keller_beheizt': 'Keller beheizt',
    'kuehlWfl': 'Kühlfläche',

    // Fenster fields
    'fenster': 'Fenster',
    'bezeichnung': 'Bezeichnung',
    'flaeche': 'Fläche',
    'art': 'Art',
    'uWert': 'U-Wert',
    'ausrichtung': 'Ausrichtung',
    'baujahr': 'Baujahr',

    // Verbrauchsdaten - Consumption amount fields
    'ETr1_Jahr1_Menge': 'Verbrauchsmenge Jahr 1 (Energieträger 1)',
    'ETr1_Jahr2_Menge': 'Verbrauchsmenge Jahr 2 (Energieträger 1)',
    'ETr1_Jahr3_Menge': 'Verbrauchsmenge Jahr 3 (Energieträger 1)',
    'ETr2_Jahr1_Menge': 'Verbrauchsmenge Jahr 1 (Energieträger 2)',
    'ETr2_Jahr2_Menge': 'Verbrauchsmenge Jahr 2 (Energieträger 2)',
    'ETr2_Jahr3_Menge': 'Verbrauchsmenge Jahr 3 (Energieträger 2)',
    'ETr3_Jahr1_Menge': 'Verbrauchsmenge Jahr 1 (Energieträger 3)',
    'ETr3_Jahr2_Menge': 'Verbrauchsmenge Jahr 2 (Energieträger 3)',
    'ETr3_Jahr3_Menge': 'Verbrauchsmenge Jahr 3 (Energieträger 3)',

    // Add more mappings as needed
  };
  
  return fieldNameMappings[fieldName] || fieldName;
}
