import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Breadcrumb } from '../Breadcrumb';
import { useCertificateType } from '../../../hooks/useCertificateType';
import { useNavigationState } from '../../../hooks/useNavigationState';
import { useLocation } from '@tanstack/react-router';

// Mock the hooks
vi.mock('../../../hooks/useCertificateType');
vi.mock('../../../hooks/useNavigationState');
vi.mock('@tanstack/react-router');

const mockUseCertificateType = vi.mocked(useCertificateType);
const mockUseNavigationState = vi.mocked(useNavigationState);
const mockUseLocation = vi.mocked(useLocation);

// Mock navigation state functions
const mockNavigationState = {
  markPageAsVisited: vi.fn(),
  isPageAccessible: vi.fn(() => true),
  isPageCompleted: vi.fn(() => false),
  isPageVisited: vi.fn(() => false)
};

describe('Breadcrumb Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUseLocation.mockReturnValue({
      pathname: '/erfassen/objektdaten'
    } as any);
    
    mockUseNavigationState.mockReturnValue(mockNavigationState);
  });

  describe('Certificate Type WG/V', () => {
    beforeEach(() => {
      mockUseCertificateType.mockReturnValue({
        certificateType: 'WG/V',
        isLoading: false
      });
    });

    it('renders correct number of pages for WG/V', () => {
      render(<Breadcrumb />);
      
      // WG/V should have 5 pages
      expect(screen.getByText('Objektdaten')).toBeInTheDocument();
      expect(screen.getByText('Gebäudedetails 1')).toBeInTheDocument();
      expect(screen.getByText('Gebäudedetails 2')).toBeInTheDocument();
      expect(screen.getByText('Verbrauch')).toBeInTheDocument();
      expect(screen.getByText('Zusammenfassung')).toBeInTheDocument();
      
      // Should not have WG/B specific pages
      expect(screen.queryByText('Fenster')).not.toBeInTheDocument();
      expect(screen.queryByText('Heizung')).not.toBeInTheDocument();
      expect(screen.queryByText('TWW & Lüftung')).not.toBeInTheDocument();
    });

    it('uses full titles for WG/V type', () => {
      render(<Breadcrumb />);
      
      // Should use full titles, not short ones
      expect(screen.getByText('Zusammenfassung')).toBeInTheDocument();
      expect(screen.queryByText('Übersicht')).not.toBeInTheDocument();
    });
  });

  describe('Certificate Type WG/B', () => {
    beforeEach(() => {
      mockUseCertificateType.mockReturnValue({
        certificateType: 'WG/B',
        isLoading: false
      });
    });

    it('renders correct number of pages for WG/B', () => {
      render(<Breadcrumb />);
      
      // WG/B should have 7 pages
      expect(screen.getByText('Objekt')).toBeInTheDocument(); // Short title
      expect(screen.getByText('Details 1')).toBeInTheDocument(); // Short title
      expect(screen.getByText('Details 2')).toBeInTheDocument(); // Short title
      expect(screen.getByText('Fenster')).toBeInTheDocument();
      expect(screen.getByText('Heizung')).toBeInTheDocument();
      expect(screen.getByText('TWW')).toBeInTheDocument(); // Short title
      expect(screen.getByText('Übersicht')).toBeInTheDocument(); // Short title
      
      // Should not have Verbrauch page
      expect(screen.queryByText('Verbrauch')).not.toBeInTheDocument();
    });

    it('uses short titles for WG/B type', () => {
      render(<Breadcrumb />);
      
      // Should use short titles for space efficiency
      expect(screen.getByText('Objekt')).toBeInTheDocument();
      expect(screen.getByText('Details 1')).toBeInTheDocument();
      expect(screen.getByText('TWW')).toBeInTheDocument();
      expect(screen.getByText('Übersicht')).toBeInTheDocument();
      
      // Should not show full titles
      expect(screen.queryByText('Objektdaten')).not.toBeInTheDocument();
      expect(screen.queryByText('Gebäudedetails 1')).not.toBeInTheDocument();
      expect(screen.queryByText('TWW & Lüftung')).not.toBeInTheDocument();
      expect(screen.queryByText('Zusammenfassung')).not.toBeInTheDocument();
    });

    it('shows tooltips with full titles when using short titles', () => {
      render(<Breadcrumb />);
      
      const objektLink = screen.getByText('Objekt').closest('a');
      expect(objektLink).toHaveAttribute('title', 'Objektdaten');
      
      const twwLink = screen.getByText('TWW').closest('a');
      expect(twwLink).toHaveAttribute('title', 'TWW & Lüftung');
    });
  });

  describe('Certificate Type NWG/V', () => {
    beforeEach(() => {
      mockUseCertificateType.mockReturnValue({
        certificateType: 'NWG/V',
        isLoading: false
      });
    });

    it('renders correct number of pages for NWG/V', () => {
      render(<Breadcrumb />);
      
      // NWG/V should have 5 pages (same as WG/V)
      expect(screen.getByText('Objektdaten')).toBeInTheDocument();
      expect(screen.getByText('Gebäudedetails 1')).toBeInTheDocument();
      expect(screen.getByText('Gebäudedetails 2')).toBeInTheDocument();
      expect(screen.getByText('Verbrauch')).toBeInTheDocument();
      expect(screen.getByText('Zusammenfassung')).toBeInTheDocument();
      
      // Should not have WG/B specific pages
      expect(screen.queryByText('Fenster')).not.toBeInTheDocument();
      expect(screen.queryByText('Heizung')).not.toBeInTheDocument();
      expect(screen.queryByText('TWW & Lüftung')).not.toBeInTheDocument();
    });
  });

  describe('Loading and Error States', () => {
    it('renders nothing when loading', () => {
      mockUseCertificateType.mockReturnValue({
        certificateType: null,
        isLoading: true
      });

      const { container } = render(<Breadcrumb />);
      expect(container.firstChild).toBeNull();
    });

    it('renders nothing when certificate type is null', () => {
      mockUseCertificateType.mockReturnValue({
        certificateType: null,
        isLoading: false
      });

      const { container } = render(<Breadcrumb />);
      expect(container.firstChild).toBeNull();
    });
  });

  describe('Responsive Behavior', () => {
    beforeEach(() => {
      mockUseCertificateType.mockReturnValue({
        certificateType: 'WG/B',
        isLoading: false
      });
    });

    it('applies responsive CSS classes', () => {
      render(<Breadcrumb />);
      
      const breadcrumbContainer = screen.getByRole('navigation');
      expect(breadcrumbContainer).toHaveClass('relative');
      
      // Check for responsive spacing classes
      const itemsContainer = breadcrumbContainer.querySelector('.flex.items-center.gap-1.sm\\:gap-2');
      expect(itemsContainer).toBeInTheDocument();
    });

    it('includes scroll functionality elements', () => {
      render(<Breadcrumb />);
      
      const breadcrumbContainer = screen.getByRole('navigation');
      
      // Should have overflow-x-auto and scrollbar-hide classes
      const scrollContainer = breadcrumbContainer.querySelector('.overflow-x-auto.scrollbar-hide');
      expect(scrollContainer).toBeInTheDocument();
    });
  });

  describe('Page State Indicators', () => {
    beforeEach(() => {
      mockUseCertificateType.mockReturnValue({
        certificateType: 'WG/V',
        isLoading: false
      });
    });

    it('shows completed state with check icon', () => {
      mockNavigationState.isPageCompleted.mockImplementation((pageType) => pageType === 'objektdaten');
      
      render(<Breadcrumb />);
      
      // Should show check icon for completed page
      const checkIcons = screen.getAllByTestId('check-icon');
      expect(checkIcons.length).toBeGreaterThan(0);
    });

    it('shows visited state with eye icon', () => {
      mockNavigationState.isPageVisited.mockImplementation((pageType) => pageType === 'objektdaten');
      mockNavigationState.isPageCompleted.mockReturnValue(false);
      
      render(<Breadcrumb />);
      
      // Should show eye icon for visited but not completed page
      const eyeIcons = screen.getAllByTestId('eye-icon');
      expect(eyeIcons.length).toBeGreaterThan(0);
    });
  });
});
