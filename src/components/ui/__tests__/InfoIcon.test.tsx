import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { InfoIcon } from '../InfoIcon';

describe('InfoIcon', () => {
  const defaultProps = {
    helpText: 'This is helpful information'
  };

  it('renders the info icon', () => {
    render(<InfoIcon {...defaultProps} />);
    
    const button = screen.getByRole('button', { name: /hilfe anzeigen/i });
    expect(button).toBeInTheDocument();
  });

  it('shows tooltip on hover', async () => {
    render(<InfoIcon {...defaultProps} />);

    const button = screen.getByRole('button', { name: /hilfe anzeigen/i });

    // Hover over the button
    fireEvent.mouseEnter(button);

    await waitFor(() => {
      expect(screen.getByRole('tooltip')).toBeInTheDocument();
      expect(screen.getByText(defaultProps.helpText)).toBeInTheDocument();
    });
  });

  it('shows tooltip on click', async () => {
    render(<InfoIcon {...defaultProps} />);

    const button = screen.getByRole('button', { name: /hilfe anzeigen/i });

    // Click the button
    fireEvent.click(button);

    await waitFor(() => {
      expect(screen.getByRole('tooltip')).toBeInTheDocument();
      expect(screen.getByText(defaultProps.helpText)).toBeInTheDocument();
    });
  });

  it('hides tooltip on second click', async () => {
    render(<InfoIcon {...defaultProps} />);

    const button = screen.getByRole('button', { name: /hilfe anzeigen/i });

    // Click to show
    fireEvent.click(button);
    await waitFor(() => {
      expect(screen.getByRole('tooltip')).toBeInTheDocument();
    });

    // Click to hide
    fireEvent.click(button);
    await waitFor(() => {
      expect(screen.queryByRole('tooltip')).not.toBeInTheDocument();
    });
  });

  it('shows tooltip on Enter key press', async () => {
    render(<InfoIcon {...defaultProps} />);

    const button = screen.getByRole('button', { name: /hilfe anzeigen/i });
    button.focus();

    // Press Enter
    fireEvent.keyDown(button, { key: 'Enter', code: 'Enter' });

    await waitFor(() => {
      expect(screen.getByRole('tooltip')).toBeInTheDocument();
      expect(screen.getByText(defaultProps.helpText)).toBeInTheDocument();
    });
  });

  it('shows tooltip on Space key press', async () => {
    render(<InfoIcon {...defaultProps} />);

    const button = screen.getByRole('button', { name: /hilfe anzeigen/i });
    button.focus();

    // Press Space
    fireEvent.keyDown(button, { key: ' ', code: 'Space' });

    await waitFor(() => {
      expect(screen.getByRole('tooltip')).toBeInTheDocument();
      expect(screen.getByText(defaultProps.helpText)).toBeInTheDocument();
    });
  });

  it('hides tooltip on Escape key press', async () => {
    render(<InfoIcon {...defaultProps} />);

    const button = screen.getByRole('button', { name: /hilfe anzeigen/i });

    // Show tooltip first
    fireEvent.click(button);
    await waitFor(() => {
      expect(screen.getByRole('tooltip')).toBeInTheDocument();
    });

    // Press Escape
    fireEvent.keyDown(button, { key: 'Escape', code: 'Escape' });

    await waitFor(() => {
      expect(screen.queryByRole('tooltip')).not.toBeInTheDocument();
    });
  });

  it('applies custom className', () => {
    const customClass = 'custom-class';
    render(<InfoIcon {...defaultProps} className={customClass} />);
    
    const button = screen.getByRole('button', { name: /hilfe anzeigen/i });
    expect(button).toHaveClass(customClass);
  });

  it('renders different sizes correctly', () => {
    const { rerender } = render(<InfoIcon {...defaultProps} size="sm" />);
    let svg = screen.getByRole('button').querySelector('svg');
    expect(svg).toHaveClass('w-4', 'h-4');

    rerender(<InfoIcon {...defaultProps} size="md" />);
    svg = screen.getByRole('button').querySelector('svg');
    expect(svg).toHaveClass('w-6', 'h-6');

    rerender(<InfoIcon {...defaultProps} size="lg" />);
    svg = screen.getByRole('button').querySelector('svg');
    expect(svg).toHaveClass('w-8', 'h-8');
  });

  it('is accessible with screen readers', async () => {
    render(<InfoIcon {...defaultProps} />);

    const button = screen.getByRole('button', { name: /hilfe anzeigen/i });

    // Initially no aria-describedby
    expect(button).not.toHaveAttribute('aria-describedby');

    // Show tooltip
    fireEvent.click(button);

    await waitFor(() => {
      const tooltip = screen.getByRole('tooltip');
      expect(tooltip).toBeInTheDocument();

      // Button should now have aria-describedby pointing to tooltip
      const tooltipId = tooltip.getAttribute('id');
      expect(button).toHaveAttribute('aria-describedby', tooltipId);
    });
  });

  it('handles mouse leave correctly', async () => {
    render(<InfoIcon {...defaultProps} />);

    const button = screen.getByRole('button', { name: /hilfe anzeigen/i });

    // Hover to show tooltip
    fireEvent.mouseEnter(button);
    await waitFor(() => {
      expect(screen.getByRole('tooltip')).toBeInTheDocument();
    });

    // Mouse leave should hide tooltip after delay
    fireEvent.mouseLeave(button);

    // Wait for the delay timeout
    await waitFor(() => {
      expect(screen.queryByRole('tooltip')).not.toBeInTheDocument();
    }, { timeout: 200 });
  });

  it('displays wider tooltip for long text content', async () => {
    const longHelpText = 'Eine Wohneinheit ist eine aus mehreren Räumen bestehende Einheit, in der ein selbstständiger Haushalt geführt werden kann. Die Räume müssen baulich von anderen Bereichen getrennt sein, einen eigenen Zugang besitzen und über die für die Haushaltsführung erforderlichen Räumlichkeiten (Küche/Kochecke, Dusche/Bad, Toilette) verfügen.';

    render(<InfoIcon helpText={longHelpText} />);

    const button = screen.getByRole('button', { name: /hilfe anzeigen/i });

    // Show tooltip
    fireEvent.click(button);

    await waitFor(() => {
      const tooltip = screen.getByRole('tooltip');
      expect(tooltip).toBeInTheDocument();
      expect(tooltip).toHaveTextContent(longHelpText);

      // Check that tooltip has improved styling classes
      expect(tooltip).toHaveClass('max-w-sm', 'sm:max-w-md', 'md:max-w-lg', 'lg:max-w-xl', 'min-w-64');
      expect(tooltip).toHaveClass('px-4', 'py-3', 'rounded-lg', 'shadow-xl');
    });
  });

  it('applies responsive width classes correctly', async () => {
    render(<InfoIcon {...defaultProps} />);

    const button = screen.getByRole('button', { name: /hilfe anzeigen/i });
    fireEvent.click(button);

    await waitFor(() => {
      const tooltip = screen.getByRole('tooltip');
      expect(tooltip).toBeInTheDocument();

      // Verify responsive width classes are applied
      expect(tooltip.className).toMatch(/max-w-sm/);
      expect(tooltip.className).toMatch(/sm:max-w-md/);
      expect(tooltip.className).toMatch(/md:max-w-lg/);
      expect(tooltip.className).toMatch(/lg:max-w-xl/);
      expect(tooltip.className).toMatch(/min-w-64/);
    });
  });
});
