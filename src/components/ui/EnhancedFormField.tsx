import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import { useField } from '@tanstack/react-form';
import { formatValidationError } from '../../utils/formValidation';
import { InfoIcon } from './InfoIcon';

interface EnhancedFormFieldProps {
  name: string;
  label: string;
  type?: string;
  placeholder?: string;
  required?: boolean;
  form: any;
  onChangeCallback?: (value: string) => void;
  helpText?: string;
  infoText?: string;
  autoComplete?: string;
}

/**
 * Enhanced FormField component with improved error display and validation
 */
export const EnhancedFormField = memo(({
  name,
  label,
  type = 'text',
  placeholder = '',
  required = true,
  form,
  onChangeCallback,
  helpText,
  infoText,
  autoComplete = 'off'
}: EnhancedFormFieldProps) => {
  const field = useField({
    form,
    name: name as any,
  });

  // Use local state for the input value to prevent focus loss
  const [localValue, setLocalValue] = useState(field.state.value ?? '');
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update local value when field value changes (from external sources)
  useEffect(() => {
    setLocalValue(field.state.value ?? '');
  }, [field.state.value]);


  const handleBlur = useCallback(() => {
    // Update the form field with the current local value on blur
    field.handleChange(localValue);
    field.handleBlur();
  }, [name, field, localValue]);

  // Use local state for immediate UI updates, debounce form updates
  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Update local state immediately for responsive UI
    setLocalValue(value);

    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Debounce form state updates to prevent re-renders during typing
    timeoutRef.current = setTimeout(() => {
      field.handleChange(value);
      // Call the callback for real-time updates
      if (onChangeCallback) {
        onChangeCallback(value);
      }
    }, 300); // 300ms debounce
  }, [name, field, onChangeCallback]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Check if field has errors
  const hasErrors = field.state.meta.errors.length > 0;
  const errors: (string | undefined)[] = field.state.meta.errors || [];

  // Format errors for better display - filter out undefined values
  const formattedErrors = errors
    .filter((error): error is string => typeof error === 'string')
    .map((error: string) => formatValidationError(error, name));

  // Add key prop to ensure stable component identity
  const fieldId = `${name}-field`;

  return (
    <div className="mb-4" key={fieldId}>
      <label htmlFor={field.name} className="block text-sm font-medium text-gray-700 mb-1">
        <span className="flex items-center gap-2">
          {label} {required && <span className="text-red-500">*</span>}
          {infoText && <InfoIcon helpText={infoText} size="sm" />}
        </span>
      </label>
      
      <div className="relative">
        <input
          id={field.name}
          name={field.name}
          type={type}
          value={typeof localValue === 'object' && localValue !== null ? '' : String(localValue)}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          autoComplete={autoComplete}
          // Add additional props to prevent focus loss
          spellCheck={false}
          autoCorrect="off"
          autoCapitalize="off"
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 transition-colors ${
            hasErrors 
              ? 'border-red-500 focus:ring-red-500 focus:border-red-500 bg-red-50' 
              : 'border-gray-300 focus:ring-green-500 focus:border-green-500'
          }`}
          aria-invalid={hasErrors}
          aria-describedby={hasErrors ? `${field.name}-error` : helpText ? `${field.name}-help` : undefined}
        />
        
        {/* Error icon */}
        {hasErrors && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
        )}
      </div>

      {/* Error messages */}
      {hasErrors && (
        <div id={`${field.name}-error`} className="mt-1">
          {formattedErrors.map((error, index) => (
            <p key={index} className="text-sm text-red-600 flex items-start">
              <svg className="h-4 w-4 text-red-500 mt-0.5 mr-1 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              {error}
            </p>
          ))}
        </div>
      )}

      {/* Help text */}
      {!hasErrors && helpText && (
        <p id={`${field.name}-help`} className="mt-1 text-sm text-gray-500">
          {helpText}
        </p>
      )}
    </div>
  );
});

EnhancedFormField.displayName = 'EnhancedFormField';
