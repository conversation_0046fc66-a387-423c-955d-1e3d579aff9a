import React from 'react';
import { fieldLabels } from '../../utils/fieldLabels';
import { enumValueLabels } from '../../utils/enumValueLabels';
import { FileWithSignedUrl } from './FileWithSignedUrl';
import { DocumentIcon } from '@heroicons/react/24/outline';

export interface DataSectionProps {
  title: string;
  data: Record<string, any> | null | undefined;
  fields?: string[] | Record<string, string>;
  excludeFields?: string[];
  filesByField?: Record<string, any[]>; // For directory-based file handling
}

/**
 * Component for displaying a section of data in a formatted table
 * Uses field labels and enum value labels for user-friendly display
 */
export const DataSection: React.FC<DataSectionProps> = ({
  title,
  data,
  fields,
  excludeFields = [],
  filesByField = {}
}) => {
  // Enhanced data processing to include file fields from filesByField
  const enhancedData = React.useMemo(() => {
    let result = data;

    // For Verbrauchsdaten section, add verbrauchsrechnung fields from filesByField if they exist
    if (title === 'Verbrauchsdaten' && filesByField) {
      result = {
        ...data,
        // Add verbrauchsrechnung fields if files exist but database fields don't
        ...(filesByField.verbrauchsrechnung1?.length > 0 ? { verbrauchsrechnung1: 'Dateien verfügbar' } : {}),
        ...(filesByField.verbrauchsrechnung2?.length > 0 ? { verbrauchsrechnung2: 'Dateien verfügbar' } : {}),
        ...(filesByField.verbrauchsrechnung3?.length > 0 ? { verbrauchsrechnung3: 'Dateien verfügbar' } : {})
      };
    }

    // For Objektdaten section, add gebaeudebild and grundriss fields from filesByField if they exist
    if (title === 'Objektdaten' && filesByField) {
      result = {
        ...data,
        // Add gebaeudebild field if files exist but database field doesn't
        ...(filesByField.gebaeudebild?.length > 0 ? { gebaeudebild: 'Dateien verfügbar' } : {}),
        // Add grundriss field if files exist but database field doesn't
        ...(filesByField.grundriss?.length > 0 ? { grundriss: 'Dateien verfügbar' } : {})
      };
    }

    return result;
  }, [title, data, filesByField]);

  if (!enhancedData || Object.keys(enhancedData).length === 0) {
    return null;
  }

  // Determine which fields to display
  const getFieldsToDisplay = () => {
    if (Array.isArray(fields)) {
      // If fields is an array, use those specific fields
      return fields.filter(field => !excludeFields.includes(field));
    } else if (fields && typeof fields === 'object') {
      // If fields is an object, use its keys
      return Object.keys(fields).filter(field => !excludeFields.includes(field));
    } else {
      // Otherwise, use all fields from enhancedData except excluded ones
      return Object.keys(enhancedData).filter(field =>
        !excludeFields.includes(field) &&
        enhancedData[field] !== null &&
        enhancedData[field] !== undefined &&
        enhancedData[field] !== ''
      );
    }
  };

  const fieldsToDisplay = getFieldsToDisplay();

  if (fieldsToDisplay.length === 0) {
    return null;
  }

  // Get display label for a field
  const getFieldLabel = (field: string): string => {
    if (fields && typeof fields === 'object' && !Array.isArray(fields)) {
      const fieldMap = fields as Record<string, string>;
      if (fieldMap[field]) {
        return fieldMap[field];
      }
    }
    return fieldLabels[field] || field;
  };

  // Get display value for a field
  const getDisplayValue = (field: string, value: any): string => {
    if (value === null || value === undefined || value === '') {
      return '-';
    }

    // Check if there's an enum mapping for this field
    if (enumValueLabels[field] && enumValueLabels[field][value]) {
      return enumValueLabels[field][value];
    }

    // Handle boolean values
    if (typeof value === 'boolean') {
      return value ? 'Ja' : 'Nein';
    }

    // Handle arrays (shouldn't normally happen in display, but just in case)
    if (Array.isArray(value)) {
      return value.join(', ');
    }

    // Handle objects (shouldn't normally happen in display, but just in case)
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }

    return String(value);
  };

  // File handling components
  const VerbrauchsrechnungFiles: React.FC<{
    files: any[];
    rechnungNumber: string;
  }> = ({ files, rechnungNumber }) => {
    if (!files || files.length === 0) {
      return <span className="text-gray-400">Keine Dateien hochgeladen</span>;
    }

    return (
      <div className="space-y-2">
        {files.map((file, index) => (
          <div key={index} className="flex items-center space-x-2">
            <FileWithSignedUrl
              path={file.path}
              label={file.originalName || `Verbrauchsrechnung ${rechnungNumber}`}
            />
            <div className="text-xs text-gray-500">
              {files.length > 1 ? `(${index + 1} von ${files.length})` : <DocumentIcon className="h-4 w-4" title="Einzeldatei" />}
            </div>
          </div>
        ))}
      </div>
    );
  };

  const GebaeudebildFiles: React.FC<{
    files: any[];
  }> = ({ files }) => {
    if (!files || files.length === 0) {
      return <span className="text-gray-400">Keine Dateien hochgeladen</span>;
    }

    return (
      <div className="space-y-2">
        {files.map((file, index) => (
          <div key={index} className="flex items-center space-x-2">
            <FileWithSignedUrl
              path={file.path}
              label={file.originalName || 'Gebäudebild'}
            />
            <div className="text-xs text-gray-500">
              {files.length > 1 ? `(${index + 1} von ${files.length})` : <DocumentIcon className="h-4 w-4" title="Einzeldatei" />}
            </div>
          </div>
        ))}
      </div>
    );
  };

  const GrundrissFiles: React.FC<{
    files: any[];
  }> = ({ files }) => {
    if (!files || files.length === 0) {
      return <span className="text-gray-400">Keine Dateien hochgeladen</span>;
    }

    return (
      <div className="space-y-2">
        {files.map((file, index) => (
          <div key={index} className="flex items-center space-x-2">
            <FileWithSignedUrl
              path={file.path}
              label={file.originalName || 'Grundriss'}
            />
            <div className="text-xs text-gray-500">
              {files.length > 1 ? `(${index + 1} von ${files.length})` : <DocumentIcon className="h-4 w-4" title="Einzeldatei" />}
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="bg-white shadow rounded-lg p-6 mb-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        {title}
      </h3>
      
      <div className="overflow-hidden">
        <table className="min-w-full table-fixed divide-y divide-gray-200">
          <colgroup>
            <col style={{ width: '60%' }} />
            <col style={{ width: '40%' }} />
          </colgroup>
          <thead className="bg-gray-100">
            <tr>
              <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                Feld
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                Wert
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {fieldsToDisplay.map((field, index) => (
              <tr key={field} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <td className="px-6 py-4 text-sm font-medium text-gray-900">
                  {getFieldLabel(field)}
                </td>
                <td className="px-6 py-4 text-sm text-gray-700 break-words">
                  {/* Special handling for file fields using directory-based files */}
                  {field && ['verbrauchsrechnung1', 'verbrauchsrechnung2', 'verbrauchsrechnung3'].includes(field) && filesByField ? (
                    <VerbrauchsrechnungFiles
                      files={filesByField[field] || []}
                      rechnungNumber={field.replace('verbrauchsrechnung', '')}
                    />
                  ) : field === 'gebaeudebild' && filesByField && filesByField.gebaeudebild?.length > 0 ? (
                    <GebaeudebildFiles files={filesByField.gebaeudebild} />
                  ) : field === 'grundriss' && filesByField && filesByField.grundriss?.length > 0 ? (
                    <GrundrissFiles files={filesByField.grundriss} />
                  ) : (
                    getDisplayValue(field, enhancedData[field])
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
