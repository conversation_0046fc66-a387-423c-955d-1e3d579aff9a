import React, { useState, useRef, useEffect } from 'react';

interface InfoIconProps {
  /** The help text to display in the tooltip */
  helpText: string;
  /** Optional CSS classes for styling */
  className?: string;
  /** Position of the tooltip relative to the icon */
  position?: 'top' | 'bottom' | 'left' | 'right';
  /** Size of the icon */
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Reusable InfoIcon component that displays contextual help text in a tooltip
 * 
 * Features:
 * - Accessible with keyboard navigation (Tab, Enter, Escape)
 * - Screen reader support with aria-describedby
 * - Hover and click interactions
 * - Responsive positioning
 * - Consistent styling with existing form components
 */
export const InfoIcon: React.FC<InfoIconProps> = ({
  helpText,
  className = '',
  position = 'top',
  size = 'lg'
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [actualPosition, setActualPosition] = useState(position);
  const iconRef = useRef<HTMLButtonElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Size classes for the icon
  const sizeClasses = {
    sm: 'w-4 h-4',    // 16px × 16px
    md: 'w-6 h-6',    // 24px × 24px
    lg: 'w-8 h-8'     // 32px × 32px
  };

  // Position classes for the tooltip
  const positionClasses = {
    top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
    bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
    left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
    right: 'left-full top-1/2 transform -translate-y-1/2 ml-2'
  };

  // Arrow classes for the tooltip
  const arrowClasses = {
    top: 'top-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent border-t-gray-800',
    bottom: 'bottom-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-t-transparent border-b-gray-800',
    left: 'left-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent border-l-gray-800',
    right: 'right-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent border-r-gray-800'
  };

  // Check if tooltip would go off-screen and adjust position
  const checkPosition = () => {
    if (!iconRef.current || !tooltipRef.current) return;

    const iconRect = iconRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const margin = 16; // Margin from viewport edges

    let newPosition = position;

    // Check horizontal overflow for top/bottom positions
    if (position === 'top' || position === 'bottom') {
      const tooltipLeft = iconRect.left + iconRect.width / 2 - tooltipRect.width / 2;
      const tooltipRight = tooltipLeft + tooltipRect.width;

      if (tooltipLeft < margin) {
        // Would overflow left, try right position if there's space
        if (iconRect.right + tooltipRect.width + margin < viewportWidth) {
          newPosition = 'right';
        }
      } else if (tooltipRight > viewportWidth - margin) {
        // Would overflow right, try left position if there's space
        if (iconRect.left - tooltipRect.width - margin > 0) {
          newPosition = 'left';
        }
      }
    }

    // Check vertical overflow and adjust accordingly
    if (newPosition === 'top' && iconRect.top - tooltipRect.height - margin < 0) {
      newPosition = 'bottom';
    } else if (newPosition === 'bottom' && iconRect.bottom + tooltipRect.height + margin > viewportHeight) {
      newPosition = 'top';
    }

    // For left/right positions, check vertical centering
    if (newPosition === 'left' || newPosition === 'right') {
      const tooltipTop = iconRect.top + iconRect.height / 2 - tooltipRect.height / 2;
      const tooltipBottom = tooltipTop + tooltipRect.height;

      if (tooltipTop < margin) {
        // Would overflow top, try bottom position
        newPosition = 'bottom';
      } else if (tooltipBottom > viewportHeight - margin) {
        // Would overflow bottom, try top position
        newPosition = 'top';
      }
    }

    setActualPosition(newPosition);
  };

  const showTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(true);
  };

  const hideTooltip = () => {
    timeoutRef.current = setTimeout(() => {
      setIsVisible(false);
    }, 150); // Small delay to allow moving to tooltip
  };

  const handleClick = () => {
    if (isVisible) {
      setIsVisible(false);
    } else {
      showTooltip();
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleClick();
    } else if (event.key === 'Escape') {
      setIsVisible(false);
    }
  };

  // Update position when tooltip becomes visible
  useEffect(() => {
    if (isVisible) {
      // Small delay to ensure tooltip is rendered
      setTimeout(checkPosition, 10);
    }
  }, [isVisible]);

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const tooltipId = `info-tooltip-${Math.random().toString(36).substring(2, 11)}`;

  return (
    <div className="relative inline-block">
      <button
        ref={iconRef}
        type="button"
        className={`inline-flex items-center justify-center text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded-full transition-colors ${className}`}
        onClick={handleClick}
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
        onKeyDown={handleKeyDown}
        aria-describedby={isVisible ? tooltipId : undefined}
        aria-label="Hilfe anzeigen"
        tabIndex={0}
      >
        <svg
          className={`${sizeClasses[size]} fill-current`}
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
            clipRule="evenodd"
          />
        </svg>
      </button>

      {/* Tooltip */}
      {isVisible && (
        <div
          ref={tooltipRef}
          id={tooltipId}
          className={`absolute z-50 px-4 py-3 text-sm text-white bg-gray-800 rounded-lg shadow-xl max-w-sm sm:max-w-md md:max-w-lg lg:max-w-xl min-w-64 ${positionClasses[actualPosition]}`}
          onMouseEnter={showTooltip}
          onMouseLeave={hideTooltip}
          role="tooltip"
          style={{
            lineHeight: '1.5',
            wordBreak: 'break-word',
            hyphens: 'auto'
          }}
        >
          <div className="leading-relaxed">
            {helpText}
          </div>

          {/* Tooltip arrow */}
          <div
            className={`absolute w-0 h-0 border-4 ${arrowClasses[actualPosition]}`}
          />
        </div>
      )}
    </div>
  );
};
