import { useState } from 'react';
import { useAdminCertificateFiles } from '../../hooks/useAdminCertificateFiles';
import { 
  formatFileSize, 
  formatUploadDate, 
  getCategoryDisplayName, 
  downloadCertificateFilesAsZip,
  getMultipleSignedUrls,
  type CategorizedFileInfo,
  type FileCategory 
} from '../../utils/adminFileUtils';
import { LoadingSpinner } from '../ui/StatusMessages';

interface CertificateFileManagerProps {
  userId: string | null;
  certificateId: string;
  orderNumber?: string | null;
}

export const CertificateFileManager = ({ userId, certificateId, orderNumber }: CertificateFileManagerProps) => {
  const { fileStats, isLoading, hasFiles } = useAdminCertificateFiles(userId, certificateId);
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadMessage, setDownloadMessage] = useState<string | null>(null);

  const handleBulkDownload = async () => {
    if (!userId) return;
    
    setIsDownloading(true);
    setDownloadMessage(null);
    
    try {
      const result = await downloadCertificateFilesAsZip(userId, certificateId, orderNumber);
      if (result.success) {
        setDownloadMessage(result.error || 'Download erfolgreich gestartet');
      } else {
        setDownloadMessage(result.error || 'Download fehlgeschlagen');
      }
    } catch (error) {
      setDownloadMessage('Fehler beim Download');
      console.error('Bulk download error:', error);
    } finally {
      setIsDownloading(false);
      // Clear message after 5 seconds
      setTimeout(() => setDownloadMessage(null), 5000);
    }
  };

  if (isLoading) {
    return (
      <div className="p-4 bg-gray-50">
        <LoadingSpinner message="Lade Dateien..." />
      </div>
    );
  }

  if (!hasFiles) {
    return (
      <div className="p-4 bg-gray-50 border-t">
        <div className="text-center text-gray-500">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
          <p className="mt-2 text-sm">Keine Dateien hochgeladen</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 bg-gray-50 border-t">
      {/* File Statistics Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <h4 className="text-sm font-medium text-gray-900">
            Hochgeladene Dateien ({fileStats.totalFiles})
          </h4>
          <span className="text-xs text-gray-500">
            Gesamt: {formatFileSize(fileStats.totalSize)}
          </span>
        </div>
        
        {/* Bulk Download Button */}
        <button
          onClick={handleBulkDownload}
          disabled={isDownloading}
          className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isDownloading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-3 w-3 text-gray-500" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Wird erstellt...
            </>
          ) : (
            <>
              <svg className="-ml-1 mr-2 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Alle als ZIP
            </>
          )}
        </button>
      </div>

      {/* Download Message */}
      {downloadMessage && (
        <div className="mb-4 p-2 bg-blue-50 border border-blue-200 rounded text-sm text-blue-700">
          {downloadMessage}
        </div>
      )}

      {/* File Categories */}
      <div className="space-y-4">
        {/* Building Images */}
        {fileStats.buildingImages > 0 && (
          <FileCategory
            files={fileStats.categories.building_images}
            title={`${getCategoryDisplayName('building_images')} (${fileStats.buildingImages})`}
          />
        )}

        {/* Consumption Bills */}
        {fileStats.consumptionBills > 0 && (
          <FileCategory
            files={fileStats.categories.consumption_bills}
            title={`${getCategoryDisplayName('consumption_bills')} (${fileStats.consumptionBills})`}
          />
        )}

        {/* Floor Plans */}
        {fileStats.floorPlans > 0 && (
          <FileCategory
            files={fileStats.categories.floor_plans}
            title={`${getCategoryDisplayName('floor_plans')} (${fileStats.floorPlans})`}
          />
        )}
      </div>
    </div>
  );
};

// File Category Component
interface FileCategoryProps {
  files: CategorizedFileInfo[];
  title: string;
}

const FileCategory = ({ files, title }: FileCategoryProps) => {
  const [signedUrls, setSignedUrls] = useState<Record<string, string | null>>({});
  const [urlsLoading, setUrlsLoading] = useState(false);

  const loadSignedUrls = async () => {
    if (Object.keys(signedUrls).length > 0) return; // Already loaded
    
    setUrlsLoading(true);
    try {
      const filePaths = files.map(f => f.path);
      const urls = await getMultipleSignedUrls(filePaths);
      setSignedUrls(urls);
    } catch (error) {
      console.error('Error loading signed URLs:', error);
    } finally {
      setUrlsLoading(false);
    }
  };

  return (
    <div className="border border-gray-200 rounded-lg">
      <div className="px-3 py-2 bg-gray-100 border-b border-gray-200">
        <h5 className="text-sm font-medium text-gray-800">{title}</h5>
      </div>
      <div className="p-3">
        <div className="grid grid-cols-1 gap-2">
          {files.map((file) => (
            <FileItem
              key={file.path}
              file={file}
              signedUrl={signedUrls[file.path]}
              onLoadUrl={loadSignedUrls}
              isLoadingUrls={urlsLoading}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

// Individual File Item Component
interface FileItemProps {
  file: CategorizedFileInfo;
  signedUrl?: string | null;
  onLoadUrl: () => void;
  isLoadingUrls: boolean;
}

const FileItem = ({ file, signedUrl, onLoadUrl, isLoadingUrls }: FileItemProps) => {
  const handleDownloadClick = () => {
    if (!signedUrl && !isLoadingUrls) {
      onLoadUrl();
    }
  };

  return (
    <div className="flex items-center justify-between p-2 bg-white border border-gray-200 rounded hover:bg-gray-50">
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        {/* File Icon */}
        <div className="flex-shrink-0">
          {file.fileType === 'pdf' ? (
            <svg className="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
            </svg>
          ) : (
            <svg className="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
          )}
        </div>

        {/* File Info */}
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900 truncate">{file.originalName}</p>
          <div className="flex items-center space-x-2 text-xs text-gray-500">
            <span>{file.displayName}</span>
            <span>•</span>
            <span>{formatFileSize(file.size)}</span>
            <span>•</span>
            <span>{formatUploadDate(file.created_at)}</span>
          </div>
        </div>
      </div>

      {/* Download Button */}
      <div className="flex-shrink-0">
        {signedUrl ? (
          <a
            href={signedUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <svg className="-ml-0.5 mr-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Download
          </a>
        ) : (
          <button
            onClick={handleDownloadClick}
            disabled={isLoadingUrls}
            className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {isLoadingUrls ? (
              <>
                <svg className="animate-spin -ml-0.5 mr-1 h-3 w-3 text-gray-500" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Laden...
              </>
            ) : (
              <>
                <svg className="-ml-0.5 mr-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Download
              </>
            )}
          </button>
        )}
      </div>
    </div>
  );
};
