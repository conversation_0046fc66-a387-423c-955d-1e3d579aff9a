import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useNavigationState } from '../hooks/useNavigationState';
import { supabase } from '../lib/supabase';

// Mock the supabase client
vi.mock('../lib/supabase', () => ({
  supabase: {
    from: vi.fn(),
    auth: {
      getUser: vi.fn()
    }
  }
}));

// Mock the certificate context
vi.mock('../contexts/CertificateContext', () => ({
  useCertificate: () => ({
    activeCertificateId: 'test-cert-id',
    setActiveCertificateId: vi.fn(),
    createNewCertificate: vi.fn(),
    getCertificateById: vi.fn(),
    loading: false
  })
}));

describe('Certificate Status Bug Fix - Payment Complete Status Protection', () => {
  const mockSupabase = supabase as any;
  
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should prevent status downgrade from payment_complete when navigating to form pages', async () => {
    // Mock certificate with payment_complete status
    const mockSelect = vi.fn().mockResolvedValue({
      data: { status: 'payment_complete', certificate_type: 'WG/V' },
      error: null
    });

    const mockUpdate = vi.fn().mockResolvedValue({
      data: null,
      error: null
    });

    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          single: mockSelect
        })
      }),
      update: () => ({
        eq: mockUpdate
      })
    });

    const { result } = renderHook(() => useNavigationState('WG/V'));

    // Wait for the hook to initialize
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    // Try to mark objektdaten page as visited (this should be prevented)
    await act(async () => {
      await result.current.markPageAsVisited('objektdaten');
    });

    // The update should not have been called because the status downgrade should be prevented
    expect(mockUpdate).not.toHaveBeenCalled();
  });

  it('should prevent status downgrade from payment_complete when navigating to gebaeudedetails1', async () => {
    // Mock certificate with payment_complete status
    const mockSelect = vi.fn().mockResolvedValue({
      data: { status: 'payment_complete', certificate_type: 'WG/B' },
      error: null
    });

    const mockUpdate = vi.fn().mockResolvedValue({
      data: null,
      error: null
    });

    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          single: mockSelect
        })
      }),
      update: () => ({
        eq: mockUpdate
      })
    });

    const { result } = renderHook(() => useNavigationState('WG/B'));

    // Wait for the hook to initialize
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    // Try to mark gebaeudedetails1 page as visited (this should be prevented)
    await act(async () => {
      await result.current.markPageAsVisited('gebaeudedetails1');
    });

    // The update should not have been called because the status downgrade should be prevented
    expect(mockUpdate).not.toHaveBeenCalled();
  });

  it('should prevent status downgrade from payment_complete when navigating to any form page', async () => {
    const formPages = ['objektdaten', 'gebaeudedetails1', 'gebaeudedetails2', 'verbrauch'];
    
    for (const pageType of formPages) {
      // Reset mocks for each iteration
      vi.clearAllMocks();
      
      // Mock certificate with payment_complete status
      const mockSelect = vi.fn().mockResolvedValue({
        data: { status: 'payment_complete', certificate_type: 'WG/V' },
        error: null
      });

      const mockUpdate = vi.fn().mockResolvedValue({
        data: null,
        error: null
      });

      mockSupabase.from.mockReturnValue({
        select: () => ({
          eq: () => ({
            single: mockSelect
          })
        }),
        update: () => ({
          eq: mockUpdate
        })
      });

      const { result } = renderHook(() => useNavigationState('WG/V'));

      // Wait for the hook to initialize
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      // Try to mark the page as visited (this should be prevented)
      await act(async () => {
        await result.current.markPageAsVisited(pageType as any);
      });

      // The update should not have been called because the status downgrade should be prevented
      expect(mockUpdate).not.toHaveBeenCalled();
    }
  });

  it('should allow normal status progression for non-payment statuses', async () => {
    // Mock certificate with objektdaten status (normal progression)
    const mockSelect = vi.fn().mockResolvedValue({
      data: { status: 'objektdaten', certificate_type: 'WG/V' },
      error: null
    });

    const mockUpdate = vi.fn().mockResolvedValue({
      data: { status: 'gebaeudedetails1' },
      error: null
    });

    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          single: mockSelect
        })
      }),
      update: () => ({
        eq: mockUpdate
      })
    });

    const { result } = renderHook(() => useNavigationState('WG/V'));

    // Wait for the hook to initialize
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    // Try to mark gebaeudedetails1 page as visited (this should be allowed)
    await act(async () => {
      await result.current.markPageAsVisited('gebaeudedetails1');
    });

    // The update should have been called because this is normal forward progression
    expect(mockUpdate).toHaveBeenCalledWith({
      status: 'gebaeudedetails1',
      updated_at: expect.any(String)
    });
  });
});
