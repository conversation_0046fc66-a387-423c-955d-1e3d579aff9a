-- Consolidate certificate status systems by merging payment_status into status field
-- This migration removes the redundant payment_status column and updates the status field
-- to handle the complete certificate lifecycle: objektdaten → ... → zusammenfassung → payment_initiated → paid

-- First, update existing records to consolidate status values
-- Priority: payment_status takes precedence over current status for final state determination
UPDATE energieausweise
SET status = CASE
  -- If payment is completed, status should be 'payment_complete'
  WHEN payment_status = 'paid' THEN 'payment_complete'
  -- If payment failed, disputed, or expired, keep those specific statuses
  WHEN payment_status = 'failed' THEN 'payment_failed'
  WHEN payment_status = 'disputed' THEN 'payment_disputed'
  WHEN payment_status = 'expired' THEN 'payment_expired'
  -- If payment is unpaid but we're at zusammenfassung, keep it as zusammenfassung (ready for payment)
  WHEN payment_status = 'unpaid' AND status = 'zusammenfassung' THEN 'zusammenfassung'
  -- For all other cases, keep the current status (form progression)
  ELSE status
END;

-- Add comment to document the new consolidated status values
COMMENT ON COLUMN energieausweise.status IS 'Certificate status: objektdaten|gebaeudedetails1|gebaeudedetails2|fenster|heizung|tww-lueftung|verbrauch|zusammenfassung (form progression), payment_initiated (checkout started), payment_complete (payment successful), payment_failed (payment failed), payment_disputed (chargeback), payment_expired (session expired)';

-- Drop the payment_status column as it's now redundant
ALTER TABLE energieausweise DROP COLUMN IF EXISTS payment_status;

-- Drop the index on payment_status since the column is being removed
DROP INDEX IF EXISTS idx_energieausweise_payment_status;

-- Create an index on the consolidated status field for better query performance
CREATE INDEX IF NOT EXISTS idx_energieausweise_status ON energieausweise(status);

-- Update any existing functions or views that might reference payment_status
-- Note: This will be handled in subsequent application code updates
