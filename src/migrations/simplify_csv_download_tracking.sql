-- Simplify CSV download tracking by adding simple columns to energieausweise table
-- This migration removes the complex tracking infrastructure and replaces it with simple last-access tracking

-- Add new columns to energieausweise table for simple last-access tracking
ALTER TABLE energieausweise 
ADD COLUMN IF NOT EXISTS admin_loaded_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS admin_loaded TEXT;

-- Add comments for the new columns
COMMENT ON COLUMN energieausweise.admin_loaded_at IS 'Timestamp when CSV was last downloaded by an admin';
COMMENT ON COLUMN energieausweise.admin_loaded IS 'Email of the admin who last downloaded the CSV';

-- Create index for efficient queries on admin_loaded_at
CREATE INDEX IF NOT EXISTS idx_energieausweise_admin_loaded_at ON energieausweise(admin_loaded_at);

-- Drop the complex tracking infrastructure if it exists
-- Note: These DROP statements use IF EXISTS to avoid errors if objects don't exist

-- Drop the admin view
DROP VIEW IF EXISTS admin_certificates_with_downloads;

-- Drop the database functions
DROP FUNCTION IF EXISTS get_recent_csv_downloads(UUID, INTEGER);
DROP FUNCTION IF EXISTS record_csv_download(UUID, TEXT, INTEGER, TEXT, INET);

-- Drop the csv_downloads table and all its dependencies
DROP TABLE IF EXISTS csv_downloads CASCADE;

-- Remove any related indexes that might have been created
DROP INDEX IF EXISTS idx_csv_downloads_certificate_id;
DROP INDEX IF EXISTS idx_csv_downloads_admin_user_id;
DROP INDEX IF EXISTS idx_csv_downloads_timestamp;
DROP INDEX IF EXISTS idx_csv_downloads_recent;
DROP INDEX IF EXISTS idx_csv_downloads_recent_by_cert;

-- Update any existing records to have NULL values for the new columns
-- (This is safe since we're starting fresh with the simplified approach)
UPDATE energieausweise 
SET admin_loaded_at = NULL, admin_loaded = NULL 
WHERE admin_loaded_at IS NOT NULL OR admin_loaded IS NOT NULL;

-- Add a comment to document the simplified approach
COMMENT ON TABLE energieausweise IS 'Energy certificate data with simplified CSV download tracking via admin_loaded_at and admin_loaded columns';

-- Grant necessary permissions for the new columns
-- (Inherits existing RLS policies from the energieausweise table)

-- Create a simple function to update the last access information
CREATE OR REPLACE FUNCTION update_csv_last_access(
  certificate_id UUID,
  admin_email TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Verify user is admin (using existing is_admin_user function)
  IF NOT is_admin_user() THEN
    RAISE EXCEPTION 'Access denied: Admin role required';
  END IF;
  
  -- Update the last access information
  UPDATE energieausweise 
  SET 
    admin_loaded_at = NOW(),
    admin_loaded = admin_email,
    updated_at = NOW()
  WHERE id = certificate_id;
  
  -- Return true if update was successful
  RETURN FOUND;
END;
$$;

-- Grant execute permission on the new function
GRANT EXECUTE ON FUNCTION update_csv_last_access(UUID, TEXT) TO authenticated;

-- Add comment for the function
COMMENT ON FUNCTION update_csv_last_access(UUID, TEXT) IS 'Updates the last CSV access timestamp and admin email for a certificate';

-- Create a helper function to get admin email from auth.users
CREATE OR REPLACE FUNCTION get_current_admin_email()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  admin_email TEXT;
BEGIN
  -- Verify user is admin
  IF NOT is_admin_user() THEN
    RETURN NULL;
  END IF;
  
  -- Get current user's email from auth.users
  SELECT email INTO admin_email
  FROM auth.users
  WHERE id = auth.uid();
  
  RETURN admin_email;
END;
$$;

-- Grant execute permission on the helper function
GRANT EXECUTE ON FUNCTION get_current_admin_email() TO authenticated;

-- Add comment for the helper function
COMMENT ON FUNCTION get_current_admin_email() IS 'Returns the email of the current admin user, or NULL if not admin';
