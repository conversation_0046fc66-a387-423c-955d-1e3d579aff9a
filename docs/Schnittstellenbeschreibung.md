| Feldname                 | WG/V | WG/B | NWG/V | Standardwert | Quelle      | Beispiel           | Bemerkung                                                                                       |
|-------------------------|------|------|--------|---------------|-------------|--------------------|-------------------------------------------------------------------------------------------------|
| ID                      | ■    | ■    | ■      |               | WG_Muster   |                    | Eindeutige ID des Gebäudes; muss Dateinamen-konform für Windows sein.                          |
| Straße                  | ■    | ■    | ■      |               | Objektstr.  |                    | Straße des Objektes                                                                             |
| Hausnr                  | ■    | ■    | ■      |               |             | 222                | Hausnummer des Objektes                                                                         |
| PLZ                     | ■    | ■    | ■      |               |             | 99423              | Postleitzahl des Objektes                                                                       |
| Ort                     | ■    | ■    | ■      |               |             | Weimar             | Ort des Objektes                                                                                |
| Kunden_Anrede          | ■    | ■    | ■      |               |             | Herr               | Anrede des Kunden                                                                               |
| Kunden_Vorname         | ■    | ■    | ■      |               |             | Max                | Vorname des Kunden                                                                             |
| Kunden_Nachname        | ■    | ■    | ■      |               |             | Mustermann         | Nachname des Kunden; fehlt Kunden_Vorname, wird Vor- und Nachname hier erwartet                |
| Kunden_Straße          | ■    | ■    | ■      |               |             | Musterstr.         | Kundenstraße; fehlt Kunden_Hausnr, Straße UND Hausnummer erwartet                              |
| Kunden_Hausnr          | ■    | ■    | ■      |               |             | 111                | Hausnummer des Kunden                                                                          |
| Kunden_PLZ             | ■    | ■    | ■      |               |             | 99425              | PLZ des Kunden                                                                                  |
| Kunden_Ort             | ■    | ■    | ■      |               |             | Weimar             | Ort des Kunden                                                                                  |
| Kunden_email           | ■    | ■    | ■      |               |             | <EMAIL>     | E-Mail des Kunden                                                                               |
| Kunden_telefon         | ■    | ■    | ■      |               |             | 03643/             | Telefonnummer des Kunden                                                                       |
| DIBt-Registriernummer  | ■    | ■    | ■      |               |             | TD-2018-123456789  | Registriernummer des Energieausweises                                                           |
| gebaeudeteilAuto       | ■    | ■    | ■      | false         |             | false              | Gibt an, ob nur ein Gebäudeteil betrachtet wird: ja [1], nein [0]                              |
| BedarfVerbrauch        | ■    | ■    | ■      |               |             | B/V                | Ausweistyp: Verbrauch [V], Bedarf [B]                                                           |
| Anlass                 | ■    | ■    | ■      | AG_VERMIETUNG |             |                    | AG_VERMIETUNG, AG_AUSHANG, AG_SONST (freiwillig); leer = AG_VERMIETUNG                         |
| Datenerhebung          | ■    | ■    | ■      | 2             |             | 2                  | Eigentümer [0], Aussteller [1], beide [2]                                                       |
| nichtWohnGeb           | ■    | ■    | ■      | 0             |             | 0                  | Wohngebäude [0], Nichtwohngebäude [1]                                                           |
| isGebaeudehuelle       | ■    | ■    | ■      | 1             |             | 1                  | Ein Gebäude [1], mehrere Gebäude [0]                                                            |
| Nutzung1_ID            | -    | -    | ■      |               |            | 91       | BWZK-ID der Nutzung                                                                      |
| Nutzung1_Flaeche       | -    | -    | ■      |               |            | 1000     | Fläche (NGF) der zugehörigen Nutzung                                                     |
| Baujahr                | ■    | ■    | ■      |               |            | 1975     | Baujahr des Objektes                                                                     |
| Modernisierung         | ■    | ■    | ■      |               |            | 2000     | Jahr der energetischen Sanierung                                                         |
| Wohnfläche             | ■    | ■    | ■      |               |            | 430      | Wohnfläche nach WohnflVO bzw. NGF bei NWG                                                |
| Raumhöhe               | -    | ■    | -      | 2.5           |            |          | Durchschnittliche Raumhöhe                                                               |
| Volumen                | -    | ■    | -      | Formel        |            |          | Gebäudevolumen; ggf. aus Wohnfläche * Raumhöhe * 1.25                                    |
| Wohneinheiten          | ■    | ■    | -      |               |            | 3        | Anzahl Wohneinheiten; nur für WG                                                         |
| Geschosse              | -    | ■    | -      | Formel        |            | 3        | Anzahl der Geschosse                                                                     |
| anbauSituation         | -    | ■    | -      | 0             |            | 0        | freistehend [0], einseitig [1], zweiseitig [2]                                           |
| Keller_beheizt         | ■    | -    | -      | 0             |            | 0        | beheizt [1], unbeheizt [0]                                                               |
| Klimatisiert           | ■    | ■    | ■      | 1             |            | 1        | Kühlanlage vorhanden [1], keine [0]                                                      |
| ergaenzendeErlaeuterungen | ■ | ■    | ■      | 1             |            | 1        | Ergänzende Erläuterungen für Energieausweis                                              |
| baujahrHzErz           | ■    | ■    | ■      |               |            | 1980     | Baujahr Wärmeerzeuger                                                                    |
| kuehlWfl               | ■    | ■    | -      | 0             |            | 25       | Kühlfläche in m²                                                                         |
| Originaldämmstandard   | ■    | ■    | ■      | 0             |            | 0        | normal [0], Niedrigenergiehaus [1], Passivhaus [2]                                       |
| bjFensterAustausch     | ■    | -    | -      |               |            |          | Optional, wenn Fenster erneuert wurden                                                   |
| Fensterlüftung         | ■    | ■    | ■      | 0             |            | 0        | ja [1], nein [0]                                                                         |
| Schachtlüftung         | ■    | ■    | ■      | 0             |            | 0        | ja [1], nein [0]                                                                         |
| L_Mit_WRG              | ■    | ■    | ■      | 0             |            | 0        | Lüftungsanlage mit WRG: ja [1], nein [0]                                                 |
| L_Ohne_WRG             | ■    | ■    | ■      | 0             |            | 0        | Lüftungsanlage ohne WRG: ja [1], nein [0]                                                |
| Boden1                 | -    | ■    | -      |               |                | Kellerdecke | Bezeichnung                                                                                         |
| Boden1_massiv          | -    | ■    | -      |               |                | kb_Massiv | Ziegel/Hohlstein [kb_Massiv], Holz [kb_Holz], Stahlbeton [kb_Stahlbeton]                            |
| Boden1_Kellerdecke     | -    | ■    | -      | 1             |                |          | Übergang zu unbeheiztem Keller (sonst Erdreich)                                                    |
| Boden1_Fläche          | -    | ■    | -      | Breite x Länge|                | 144      | Fläche (Außenkanten)                                                                               |
| Boden1_U-Wert          | -    | ■    | -      | Formel        |                | 0,194    | U-Wert in W/m²K; falls nicht angegeben, aus Katalog gebildet                                       |
| Boden1_Dämmung         | ■    | ■    | ■      | 0             |                |          | Nachträgliche Dämmung in cm                                                                         |
| Dach1                  | -    | ■    | -      |               |                | Kehlbalkendecke | Bezeichnung                                                                                   |
| Dach1_massiv           | -    | ■    | -      |               |                | 0        | Massivdecke [1], Holzbalken [0]                                                                    |
| Dach1_Geschossdecke    | -    | ■    | -      | 0             |                | 0        | Übergang zu unbeheiztem Dachraum                                                                   |
| Dach1_Fläche           | -    | ■    | -      |               |                | 60,6     | Fläche (Außenkanten)                                                                               |
| Dach1_U-Wert           | -    | ■    | -      | Formel        |                | 0,32     | U-Wert in W/m²K                                                                                     |
| Dach1_Dämmung          | ■    | ■    | ■      | 18            |                |          | Nachträgliche Dämmung in cm                                                                        |
| Wand1                  | -    | ■    | -      |               |                | Außenwand | Bezeichnung                                                                                         |
| Wand1_massiv           | -    | ■    | -      |               |                | kb_zweischaligOhneDaemm | Viele Optionen wie kb_Massiv, kb_Holz, kb_Stahlbeton, usw.                                 |
| Wand1_Fläche           | -    | ■    | -      | Breite x Geschosse x 2,75 | | 193,5    | Nettofläche (ohne Fenster)                                                                          |
| Wand1_U-Wert           | -    | ■    | -      | Formel        |                | 1,30     | U-Wert in W/m²K                                                                                     |
| Wand1_Dämmung          | ■    | ■    | ■      |               |                |          | Nachträgliche Dämmung in cm (Wand2, Wand3, ...)                                                    |
| Fenster1               | -    | ■    | -      |               |                | Dachflächenfenster | Bezeichnung                                                                                   |
| Fenster1_Art           | -    | ■    | -      | Formel        |                | fb_KunststoffWSG | Fensterart (z. B. fb_HolzEinfach, fb_KunststoffWSG, usw.)                                  |
| Fenster1_Fläche        | -    | ■    | -      | Formel        |                | 8,1      | Fläche (inkl. Rahmen); sonst geschätzt                                                             |
| Fenster1_U-Wert        | -    | ■    | -      | Formel        |                | 3        | Gesamt-U-Wert (Uw); sonst aus Art ermittelt                                                        |
| Fenster1_Ausrichtung   | -    | ■    | -      |               |                | 90       | 0=Nord, 90=Ost usw.                                                                                |
| Fenster1_Baujahr       | -    | ■    | -      |               |                |          | Jahr des letzten Austauschs                                                                        |
| Hzg_Baujahr                 | ■    | ■    | ■      | Gebäude-Baujahr      |            | 1995     | Wenn nicht bekannt: ½ von 25 Jahren                                                            |
| Hzg_Speicher_Baujahr        | -    | ■    | -      |                      |            | 2000     | Baujahr des Pufferspeichers, falls vorhanden                                                   |
| Hzg_Verteilung_Baujahr      | -    | ■    | -      | Gebäude-Baujahr      |            | 2000     | Baujahr der Verteilleitungen                                                                   |
| Hzg_Übergabe                | -    | ■    | -      | 0                    |            | Heizkörper | 0 = Heizkörper, 1 = Flächenheizung                                                            |
| Hzg_Verteilung_Art          | -    | ■    | -      | 2                    |            |          | 0 = dezentral, 1 = gebäudezentral, 2 = wohnungszentral                                         |
| Hzg_kreistemperatur         | -    | ■    | -      | 1                    |            | HKTEMP_70_55 | 90/70 [HKTEMP_90_70], 70/55 [HKTEMP_70_55], 55/45, 35/28                                      |
| Hzg_Verteilung_Dämmung      | -    | ■    | -      | 1                    |            |          | nachträglich gedämmt: ja [1], nein [0]                                                         |
| Hzg_Speicher                | -    | ■    | -      | 1                    |            |          | Pufferspeicher vorhanden: ja [1], nein [0]                                                     |
| Hzg_Aufstellung             | -    | ■    | -      | HZ_ZENTRALHEIZUNG    |            |          | z. B. HZ_EINZELOFEN, HZ_ETAGENHEIZUNG, HZ_AUSSERHALB                                           |
| Hzg_Technik                 | -    | ■    | -      | HZT_STD_KESSEL       |            |          | Z. B. HZT_KOLLEKTOR, HZT_BW_GERAET, HZT_FERNHZ, HZT_FC                                         |
| Hzg_Energieträger           | -    | ■    | -      | BK_GAS               |            |          | Z. B. BK_OEL, BK_STROM, BK_PELLET, BK_FW70, usw.                                               |
| Hzg_PrimFaktor              | -    | ■    | -      | 1                    |            |          | Primärenergiefaktor; relevant bei Fernwärme                                                    |
| TW_Baujahr                  | -    | ■    | -      |                      |            | 2000     | Baujahr der Trinkwarmwasseranlage                                                              |
| TW_Speicher_Baujahr         | -    | ■    | -      |                      |            | 1995     | Baujahr des TW-Speichers                                                                       |
| TW_Verteilung_Baujahr       | -    | ■    | -      |                      |            | 2000     | Baujahr der TW-Leitungen                                                                       |
| TW_Verteilung_Art           | -    | ■    | -      | 1                    |            |          | 0 = dezentral, 1 = gebäudezentral, 2 = wohnungszentral                                         |
| TW_Verteilung_Dämmung       | -    | ■    | -      | 1                    |            |          | gedämmt: ja [1], nein [0]                                                                      |
| TW_Zirkulation              | -    | ■    | -      | 1                    |            |          | zirkulierend: ja [1], nein [0]                                                                 |
| TW_Speicher_Standort        | -    | ■    | -      | 2                    |            |          | 1 = innerhalb, 2 = Keller, 3 = Dach                                                             |
| TW_Technik                  | -    | ■    | -      | WT_HZG               |            |          | Z. B. WT_EDL, WT_SOLAR, WT_WP, WT_FERNW                                                        |
| TW_Solar                    | ■    | ■    | ■      | 0                    |            | 0        | TW-Solaranlage: ja [1], nein [0]                                                               |
| HZ_Solar                    | ■    | -    | ■      | 0                    |            | 0        | HZ-Solaranlage: ja [1], nein [0]                                                               |
| TW_WP                       | ■    | -    | ■      | 0                    |            | 0        | TW-Wärmepumpe: ja [1], nein [0]                                                                |
| HZ_WP                       | ■    | -    | ■      | 0                    |            | 0        | HZ-Wärmepumpe: ja [1], nein [0]                                                                |
| Luft_Baujahr                  | -    | ■    | -      |                    |               | 2000           | Baujahr der Lüftungsanlage (falls vorhanden und ≠ Gebäudebaujahr)                                |
| Luft_Verteilung_Baujahr       | -    | ■    | -      |                    |               | 2000           | Baujahr der Luftleitungen                                                                         |
| Luft_Lage                     | -    | ■    | -      | GAO_INNERHALB      |               |                | innerhalb [GAO_INNERHALB], Keller [GAO_KELLER], Dach [GAO_DACHRAUM]                              |
| Luft_Typ                      | -    | ■    | -      | LA_FREI            |               |                | z. B. LA_ABL, LA_WRG, LA_WP                                                                       |
| ETr1_Kategorie                | ■    | -    | ■      |                    |               |                | Energieträgerkategorie (siehe Hzg_Energieträger)                                                 |
| ETr1_Heizung                  | ■    | -    | ■      |                    |               | 1              | Für Heizung verwendet: ja [1], nein [0]                                                           |
| ETr1_TWW                      | ■    | -    | ■      |                    |               | 1              | Für Trinkwasser verwendet: ja [1], nein [0]                                                       |
| ETr1_ZusatzHz                 | -    | -    | ■      |                    |               | 0              | Zusatzheizung: ja [1], nein [0]                                                                   |
| ETr1_Lueften                  | -    | -    | ■      |                    |               | 0              | Für Lüftung verwendet: ja [1], nein [0]                                                           |
| ETr1_Licht                    | -    | -    | ■      |                    |               | 0              | Für Licht verwendet: ja [1], nein [0]                                                             |
| ETr1_Kuehlen                  | -    | -    | ■      |                    |               | 0              | Für Kühlung verwendet: ja [1], nein [0]                                                           |
| ETr1_Sonst                    | -    | -    | ■      |                    |               | 0              | Für sonstige Verwendung: ja [1], nein [0]                                                         |
| ETr1_PrimFaktor              | ■    | -    | ■      |                    |               | 1              | Primärenergiefaktor; nur bei Fernwärme                                                            |
| ETr1_Anteil_erneuerbar        | ■    | -    | ■      |                    |               |                | Anteil erneuerbar; nur bei Fernwärme                                                              |
| ETr1_Anteil_KWK               | ■    | -    | ■      |                    |               |                | KWK-Anteil (nur bei Fernwärme)                                                                   |
| ETr1_isFw                     | ■    | -    | ■      |                    |               |                | Energieträger ist Fernwärme: ja [1], nein [0]                                                     |
| ETr1_gebaeudeNahErzeugt       | ■    | -    | ■      |                    |               |                | Energie gebäudenah erzeugt: ja [1], nein [0]                                                      |
| ETr1_Name                     | ■    | -    | ■      |                    |               |                | Name des Energieträgers                                                                          |
| ETr1_Jahr1_von                | ■    | -    | ■      |                    |               | dd.mm.yyyy     | Beginn Messperiode 1                                                                              |
| ETr1_Jahr1_bis                | ■    | -    | ■      |                    |               | dd.mm.yyyy     | Ende Messperiode 1                                                                                |
| ETr1_Jahr1_Menge              | ■    | -    | ■      |                    |               |                | Gesamtmenge; Einheit laut EVEBI Energieträgerdatenbank                                           |
| ETr1_Jahr1_Menge_TWW          | ■    | -    | ■      |                    |               |                | Menge für Trinkwasser (separat erfasst, gleiche Einheit wie Gesamt)                              |
| ETr1_Jahr1_Leerstand          | ■    | -    | ■      |                    |               |                | Leerstand in % der Periode 1                                                                     |
| ETr1_Jahr2_von bis Menge ...  | ■    | -    | ■      |                    |               |                | Analog zu Jahr 1 – Felder wiederholen sich für Jahr 2 und 3                                       |
| WSchVo77_erfuellt  | ■    | -    | -      | 1             |        | 1        | Erfüllt das Gebäude die Wärmeschutzverordnung von 1977? nein [0], ja [1] |

