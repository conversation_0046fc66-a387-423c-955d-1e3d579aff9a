# Pricing Display in Quiz Results

## Overview

This document describes the implementation of pricing information display in the certificate type quiz results section on the HomePage.

## Implementation Details

### Location
- **File**: `src/pages/HomePage.tsx`
- **Component**: `CertificateTypeQuiz`
- **Section**: Quiz result (step 7)

### Changes Made

1. **Added Pricing Query**: Added a `useQuery` hook to fetch pricing information from the Supabase database using the `PricingService`.

2. **Helper Function**: Created `getQuizPrice()` function to retrieve the price for a specific certificate type with proper error handling and fallback values.

3. **UI Enhancement**: Modified the green result box to prominently display the price alongside the certificate type recommendation.

### Code Structure

```typescript
// Fetch pricing information for quiz results
const { data: pricingData, isLoading: pricingLoading, error: pricingError } = useQuery({
  queryKey: ['pricing', 'all'],
  queryFn: () => PricingService.getPricingDisplayInfo(),
  retry: false,
});

// Helper function to get price for certificate type
const getQuizPrice = (certificateType: CertificateType) => {
  if (pricingLoading) return '...';
  if (pricingError || !pricingData) return '49,00 €'; // Fallback price
  
  const pricing = pricingData.find(p => p.certificate_type === certificateType);
  return pricing?.price_euros || '49,00 €';
};
```

### UI Display

The price is displayed in the green result box with:
- **Font Size**: `text-2xl` (large and prominent)
- **Font Weight**: `font-bold` (bold for emphasis)
- **Color**: `text-green-700` (matches the green theme)
- **Position**: Between the certificate type title and description

### Error Handling

1. **Loading State**: Shows "..." while pricing data is being fetched
2. **Error State**: Falls back to "49,00 €" if pricing service fails
3. **Missing Data**: Uses fallback price if specific certificate type pricing is not found

### German Localization

- Prices are formatted using German conventions (comma as decimal separator)
- Format: "XX,XX €" (e.g., "49,00 €")
- Consistent with existing pricing displays throughout the application

### Testing

- Created comprehensive tests in `src/pages/__tests__/HomePage.pricing.test.tsx`
- Tests cover successful pricing fetch, error scenarios, and price formatting
- All tests pass successfully

### Benefits

1. **Transparency**: Users see the exact cost before proceeding to create a certificate
2. **Consistency**: Uses the same pricing service as other parts of the application
3. **Flexibility**: Prices can be updated by administrators and will reflect immediately
4. **User Experience**: Clear, prominent display of pricing information in the decision flow

### Future Enhancements

- Could add currency conversion for international users
- Could display promotional pricing or discounts
- Could show comparison pricing between certificate types
