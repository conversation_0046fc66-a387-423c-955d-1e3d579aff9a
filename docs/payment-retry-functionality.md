# Payment Retry Functionality - Complete Documentation

## Table of Contents
1. [Overview](#overview)
2. [Business Logic](#business-logic)
3. [Technical Implementation](#technical-implementation)
4. [User Experience Flow](#user-experience-flow)
5. [Status Transitions](#status-transitions)
6. [Security Considerations](#security-considerations)
7. [Testing Scenarios](#testing-scenarios)
8. [Troubleshooting](#troubleshooting)

## Overview

The payment retry functionality enables users to retry payments after failures, timeouts, or disputes while preventing double payments after successful transactions. This implementation follows e-commerce best practices and provides clear user feedback throughout the payment process.

### Key Features
- ✅ **Retry Available**: After payment failures, session expiration, or disputes
- 🚫 **Retry Blocked**: Only after successful payment completion
- 🎨 **Visual Feedback**: Color-coded status messages and buttons
- 📱 **User Guidance**: Clear instructions for each payment state
- 🔒 **Security**: Prevents double charging and unauthorized retries

## Business Logic

### Payment Retry Rules

| Payment Status | Retry Allowed | Button Display | User Action |
|---------------|---------------|----------------|-------------|
| `zusammenfassung` | ✅ Yes | Green "Energieausweis kaufen" | Initial payment |
| `payment_initiated` | ⏳ In Progress | Blue "Zahlung läuft..." | Wait for completion |
| `payment_complete` | 🚫 **NO** | Green "Bezahlt & Abgeschlossen" | View certificate |
| `payment_failed` | ✅ **YES** | Orange "Zahlung erneut versuchen" | Retry payment |
| `payment_expired` | ✅ **YES** | Orange "Zahlung erneut versuchen" | Retry payment |
| `payment_disputed` | ✅ **YES** | Orange "Zahlung erneut versuchen" | Retry payment |

### E-commerce Best Practices Implemented

1. **Failure Recovery**: Users can retry after any non-success outcome
2. **Double Payment Prevention**: No retry after successful payment
3. **Clear Communication**: Specific messages for each failure type
4. **User Empowerment**: Always provide next steps and options
5. **Security First**: Validate status transitions server-side

## Technical Implementation

### Granular Status Checks

The implementation replaces the overly restrictive `isPaymentStatus` with granular checks:

```typescript
// NEW: Granular status checks for better retry handling
const isPaymentInProgress = energieausweisData?.status === 'payment_initiated';
const isPaymentFailed = energieausweisData?.status && 
  ['payment_failed', 'payment_disputed', 'payment_expired'].includes(energieausweisData.status);
const isPaid = energieausweisData?.status === 'payment_complete';

// OLD: Overly restrictive (blocked all payment-related statuses)
const isPaymentStatus = energieausweisData?.status &&
  ['payment_initiated', 'payment_complete', 'payment_failed', 'payment_disputed', 'payment_expired'].includes(energieausweisData.status);
```

### Status Transition Rules

#### Before (Restrictive)
```typescript
// Prevented ALL payment-related status transitions
if (['payment_initiated', 'payment_complete', 'payment_failed', 'payment_disputed', 'payment_expired'].includes(currentData.status)) {
  return null; // Block transition
}
```

#### After (Retry-Friendly)
```typescript
// Only prevent transitions from successful payment
if (currentData?.status === 'payment_complete') {
  return null; // Block only after success
}
// Allow transitions from failed states for retry
```

### Button Rendering Logic

```typescript
{isPaid ? (
  // Show success state - no retry
  <div className="text-green-600">Bezahlt & Abgeschlossen</div>
) : isPaymentInProgress ? (
  // Show loading state - payment in progress
  <div className="text-blue-600">Zahlung läuft...</div>
) : (
  // Show payment/retry button
  <button 
    className={isPaymentFailed ? 'bg-orange-600' : 'bg-green-600'}
    onClick={handleCheckout}
  >
    {isPaymentFailed ? 'Zahlung erneut versuchen' : 'Energieausweis kaufen'}
  </button>
)}
```

### User-Friendly Status Messages

```typescript
{isPaymentFailed && (
  <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-8">
    <p className="text-yellow-700 font-medium">
      {energieausweisData?.status === 'payment_failed' && 'Zahlung fehlgeschlagen - Sie können es erneut versuchen.'}
      {energieausweisData?.status === 'payment_expired' && 'Zahlungssitzung abgelaufen - Bitte versuchen Sie es erneut.'}
      {energieausweisData?.status === 'payment_disputed' && 'Zahlung wurde bestritten - Bitte kontaktieren Sie den Support oder versuchen Sie eine neue Zahlung.'}
    </p>
    <p className="text-yellow-600 mt-1">
      Keine Sorge - es wurden keine Gebühren erhoben. Sie können die Zahlung jederzeit wiederholen.
    </p>
  </div>
)}
```

## User Experience Flow

### Scenario 1: Successful Payment
1. User clicks "Energieausweis kaufen" (Green button)
2. Status changes to `payment_initiated` → "Zahlung läuft..." (Blue)
3. User completes payment in Stripe
4. Status changes to `payment_complete` → "Bezahlt & Abgeschlossen" (Green)
5. **No retry option available** ✅

### Scenario 2: Payment Failure
1. User clicks "Energieausweis kaufen" (Green button)
2. Status changes to `payment_initiated` → "Zahlung läuft..." (Blue)
3. Payment fails in Stripe
4. Status changes to `payment_failed` → Yellow warning message
5. **Retry button appears**: "Zahlung erneut versuchen" (Orange) ✅
6. User can retry immediately

### Scenario 3: Session Expiration
1. User starts payment but doesn't complete within 24 hours
2. Stripe expires the session
3. Status changes to `payment_expired` → Yellow warning message
4. **Retry button appears**: "Zahlung erneut versuchen" (Orange) ✅
5. User can start fresh payment

### Scenario 4: Payment Dispute
1. User completes payment successfully
2. Later, user disputes the charge with their bank
3. Status changes to `payment_disputed` → Yellow warning message
4. **Retry button appears**: "Zahlung erneut versuchen" (Orange) ✅
5. User can make new payment while dispute is resolved

## Status Transitions

```mermaid
stateDiagram-v2
    [*] --> zusammenfassung
    zusammenfassung --> payment_initiated : User clicks Pay
    payment_initiated --> payment_complete : Payment succeeds
    payment_initiated --> payment_failed : Payment fails
    payment_initiated --> payment_expired : Session expires
    payment_complete --> payment_disputed : User disputes charge
    
    payment_failed --> payment_initiated : User retries
    payment_expired --> payment_initiated : User retries
    payment_disputed --> payment_initiated : User retries
    
    payment_complete --> [*] : Final state
    
    note right of payment_complete : No retry allowed
    note right of payment_failed : Retry available
    note right of payment_expired : Retry available
    note right of payment_disputed : Retry available
```

## Security Considerations

### Double Payment Prevention
- **Server-side validation**: Status transitions validated in database
- **Idempotency**: Prevent duplicate payment sessions
- **Status locking**: Once `payment_complete`, no further transitions allowed

### Authorization Checks
- **User ownership**: Verify user owns the certificate
- **Session validation**: Validate active user session
- **CSRF protection**: Use proper form tokens

### Audit Trail
- **Payment attempts**: Log all payment initiation attempts
- **Status changes**: Track all status transitions with timestamps
- **Webhook events**: Log all Stripe webhook events for debugging

## Testing Scenarios

### 1. Normal Payment Flow
```
✅ Test: User completes payment successfully
Expected: No retry button, success message displayed
```

### 2. Payment Failure Retry
```
✅ Test: Simulate payment failure → verify retry button → test retry
Expected: Orange retry button appears, retry works correctly
```

### 3. Session Expiration
```
✅ Test: Let payment session expire → verify retry option
Expected: Yellow warning message, retry button available
```

### 4. Successful Payment Protection
```
✅ Test: After successful payment, verify no retry button
Expected: Green success message, no payment options
```

### 5. Status Message Accuracy
```
✅ Test: Verify appropriate messages for each payment state
Expected: Correct color coding and messaging per status
```

### 6. Concurrent Access
```
✅ Test: Multiple browser tabs, ensure consistent state
Expected: Status updates reflected across all sessions
```

### 7. Network Interruption
```
✅ Test: Interrupt network during payment process
Expected: Graceful handling, retry option available
```

## Troubleshooting

### Common Issues

#### Issue: Retry button not appearing after payment failure
**Cause**: Status not properly updated by webhook
**Solution**: Check webhook logs, verify Stripe webhook configuration

#### Issue: User can retry after successful payment
**Cause**: Status transition logic not working
**Solution**: Verify `isPaid` check and database status

#### Issue: Payment button disabled unexpectedly
**Cause**: Legal consent checkboxes not checked
**Solution**: Ensure all required checkboxes are selected

#### Issue: Status messages not updating
**Cause**: React query cache not invalidating
**Solution**: Check query invalidation after status updates

### Monitoring and Alerts

1. **Payment Conversion Rate**: Monitor for drops indicating retry issues
2. **Failed Payment Recovery**: Track how many failed payments are retried
3. **Double Payment Incidents**: Alert on any duplicate successful payments
4. **Status Transition Errors**: Monitor database constraint violations

## Code Examples

### Complete Implementation in ZusammenfassungPage.tsx

#### Status Check Implementation
```typescript
// File: src/pages/erfassen/ZusammenfassungPage.tsx
// Lines: 1261-1269

// More granular payment status checks for better retry handling
const isPaymentInProgress = energieausweisData?.status === 'payment_initiated';
const isPaymentFailed = energieausweisData?.status &&
  ['payment_failed', 'payment_disputed', 'payment_expired'].includes(energieausweisData.status);
const isPaid = energieausweisData?.status === 'payment_complete';

// Legacy check for backward compatibility (used in other parts of the component)
const isPaymentStatus = energieausweisData?.status &&
  ['payment_initiated', 'payment_complete', 'payment_failed', 'payment_disputed', 'payment_expired'].includes(energieausweisData.status);
```

#### Status Transition Mutations
```typescript
// File: src/pages/erfassen/ZusammenfassungPage.tsx
// Lines: 1296-1301

// Only prevent regression from successful payment - allow retry from failed states
if (currentData?.status === 'payment_complete') {
  console.log(`Status update prevented: Cannot regress from successful payment status '${currentData.status}' to 'zusammenfassung'`);
  return null;
}

// Lines: 1407-1411
// Only prevent retry from successful payment - allow retry from failed states
if (currentData?.status === 'payment_complete') {
  console.log(`Status update prevented: Cannot retry payment after successful completion '${currentData.status}'`);
  return null;
}
```

#### Button Rendering Logic
```typescript
// File: src/pages/erfassen/ZusammenfassungPage.tsx
// Lines: 2149-2191

{isPaid ? (
  <div className="flex items-center space-x-3">
    <div className="flex items-center text-green-600">
      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span className="text-lg font-medium">Bezahlt & Abgeschlossen</span>
    </div>
  </div>
) : isPaymentInProgress ? (
  <div className="flex items-center space-x-3">
    <div className="flex items-center text-blue-600">
      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span className="text-lg font-medium">Zahlung läuft...</span>
    </div>
  </div>
) : (
  <button
    className={`px-6 py-3 rounded-lg transition-colors text-lg font-medium ${
      isProcessingPayment || !legalConsent.agb || !legalConsent.datenschutz || !legalConsent.widerruf || !legalConsent.dataAccuracy
        ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
        : isPaymentFailed
          ? 'bg-orange-600 text-white hover:bg-orange-700'
          : 'bg-green-600 text-white hover:bg-green-700'
    }`}
    onClick={handleCheckout}
    disabled={isProcessingPayment || !energieausweisData || !legalConsent.agb || !legalConsent.datenschutz || !legalConsent.widerruf || !legalConsent.dataAccuracy}
  >
    {isProcessingPayment
      ? 'Zahlung wird verarbeitet...'
      : (!legalConsent.agb || !legalConsent.datenschutz || !legalConsent.widerruf || !legalConsent.dataAccuracy)
        ? 'Bitte stimmen Sie allen rechtlichen Bestimmungen zu'
        : isPaymentFailed
          ? 'Zahlung erneut versuchen'
          : isAnonymous
            ? 'Weiter zur Zahlung'
            : 'Energieausweis kaufen'
    }
  </button>
)}
```

## Integration with Existing Systems

### Webhook Integration
The payment retry functionality works seamlessly with existing Stripe webhooks:

```typescript
// File: supabase/functions/stripe-webhook/index.ts
// Webhook events that trigger retry-eligible statuses:

case 'checkout.session.expired':
  // Sets status to 'payment_expired' → Retry available

case 'charge.failed':
  // Sets status to 'payment_failed' → Retry available

case 'charge.dispute.created':
  // Sets status to 'payment_disputed' → Retry available

case 'checkout.session.completed':
  // Sets status to 'payment_complete' → No retry (final state)
```

### Database Schema Compatibility
The implementation uses the existing `status` field in the `energieausweise` table:

```sql
-- Existing status values (no schema changes required)
ALTER TYPE certificate_status ADD VALUE IF NOT EXISTS 'payment_initiated';
ALTER TYPE certificate_status ADD VALUE IF NOT EXISTS 'payment_complete';
ALTER TYPE certificate_status ADD VALUE IF NOT EXISTS 'payment_failed';
ALTER TYPE certificate_status ADD VALUE IF NOT EXISTS 'payment_disputed';
ALTER TYPE certificate_status ADD VALUE IF NOT EXISTS 'payment_expired';
```

## Performance Considerations

### Query Optimization
- **Single status check**: Granular checks use single field comparison
- **Cached queries**: React Query caches certificate status
- **Minimal re-renders**: Status changes trigger targeted updates

### User Experience Optimization
- **Immediate feedback**: Status messages appear instantly
- **Progressive enhancement**: Works without JavaScript (basic functionality)
- **Responsive design**: Button states adapt to screen size

## Migration Guide

### Updating from Previous Implementation

1. **No database changes required** - Uses existing status field
2. **Backward compatible** - Legacy `isPaymentStatus` maintained
3. **Gradual rollout** - Can be deployed without breaking existing functionality

### Testing Migration
```bash
# 1. Deploy new frontend code
npm run build && npm run deploy

# 2. Test payment flows
npm run test:payment-flows

# 3. Monitor webhook processing
# Check Supabase function logs for any errors

# 4. Verify user experience
# Test all payment scenarios in staging environment
```

This documentation provides comprehensive coverage of the payment retry functionality, ensuring both developers and stakeholders understand the implementation, business logic, and user experience improvements.
