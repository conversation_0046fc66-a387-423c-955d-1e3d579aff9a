# GebaeudeformPage Data Fetching Implementation

## Overview

This document describes the data fetching implementation in `GebaeudeformPage.tsx` that enables the `WallAreaCalculator` component to access both `gebaeudedetails1` and `gebaeudedetails2` data for performing wall area calculations.

## Problem Statement

The `WallAreaCalculator` component in `GebaeudeformPage.tsx` needed access to:
- **<PERSON><PERSON><PERSON>** (Number of floors) from `gebaeudedetails1`
- **<PERSON><PERSON>h<PERSON>he** (Room height) from `gebaeudedetails1`

However, the original implementation only fetched `gebaeudedetails2` data, causing the calculator to display "Nicht verfügbar" (Not available) for these required values.

## Solution Implementation

### 1. Modified Query Pattern

**Before:**
```typescript
const { data: existingData, isError, error } = useQuery({
  queryKey: ['energieausweise', 'gebaeudeform', activeCertificateId],
  queryFn: async () => {
    if (!activeCertificateId) return null;

    const { data, error } = await supabase
      .from('energieausweise')
      .select('gebaeudedetails2')  // Only fetched gebaeudedetails2
      .eq('id', activeCertificateId)
      .single();

    if (error) {
      throw error;
    }
    return data;
  },
  enabled: !!activeCertificateId,
  retry: false,
});
```

**After:**
```typescript
const { data: existingData, isError, error } = useQuery({
  queryKey: ['energieausweise', 'gebaeudeform', activeCertificateId],
  queryFn: async () => {
    if (!activeCertificateId) return null;

    const { data, error } = await supabase
      .from('energieausweise')
      .select('gebaeudedetails2, gebaeudedetails1')  // Now fetches both datasets
      .eq('id', activeCertificateId)
      .single();

    if (error) {
      throw error;
    }
    return data;
  },
  enabled: !!activeCertificateId,
  retry: false,
});
```

### 2. Data Structure

The query now returns data with both datasets:

```typescript
interface QueryResult {
  gebaeudedetails1?: {
    Geschosse: string;        // e.g., "2"
    Raumhöhe: string;         // e.g., "2,8"
    Baujahr?: string;         // e.g., "1990"
    // ... other fields
  };
  gebaeudedetails2?: {
    buildingShape: string;    // e.g., "rectangular"
    waende: Bauteil[];        // Wall configurations
    // ... other fields
  };
}
```

### 3. WallAreaCalculator Integration

The `WallAreaCalculator` component accesses the data as follows:

```typescript
const WallAreaCalculator = ({ flaecheField, wallWidthField, index, existingData }) => {
  // Get previously entered values from gebaeudedetails1
  const gebaeudedetails1Data = existingData?.gebaeudedetails1 as any;
  const geschosseValue = gebaeudedetails1Data?.Geschosse || '';
  const raumhoeheValue = gebaeudedetails1Data?.Raumhöhe || '';

  // Calculate area: Width × Number of Levels × Room Height
  useEffect(() => {
    const wallWidth = wallWidthField.state.value || '';
    const width = parseFloat(wallWidth.replace(',', '.'));
    const levels = parseFloat(geschosseValue);
    const roomHeight = parseFloat(raumhoeheValue.replace(',', '.'));

    if (!isNaN(width) && !isNaN(levels) && !isNaN(roomHeight) && 
        width > 0 && levels > 0 && roomHeight > 0) {
      const area = width * levels * roomHeight;
      const formattedArea = area.toFixed(2).replace('.', ',');
      setCalculatedArea(formattedArea);
      flaecheField.handleChange(formattedArea);
    } else {
      setCalculatedArea('');
      if (wallWidth === '') {
        flaecheField.handleChange('');
      }
    }
  }, [wallWidthField.state.value, geschosseValue, raumhoeheValue, flaecheField]);
```

## Reference Implementation

This implementation follows the same pattern used in `GebaeudedetailsPage2.tsx`, which successfully fetches both datasets:

```typescript
// GebaeudedetailsPage2.tsx query
.select('gebaeudedetails2, gebaeudedetails1, certificate_type, heizung, trinkwarmwasser')

// GebaeudeformPage.tsx query (streamlined for specific needs)
.select('gebaeudedetails2, gebaeudedetails1')
```

## Wall Area Calculation Formula

The calculator uses the following formula:

```
Total Wall Area = Wall Width × Number of Floors × Room Height
```

**Example:**
- Wall Width: 12.5 m (user input)
- Number of Floors: 2 (from gebaeudedetails1.Geschosse)
- Room Height: 2.8 m (from gebaeudedetails1.Raumhöhe)
- **Result:** 12.5 × 2 × 2.8 = 70.00 m²

## Localization Support

The implementation handles German decimal format:
- Input: "12,5" → Converted to 12.5 for calculation
- Output: 70.00 → Formatted as "70,00" for display

## Error Handling

The calculator gracefully handles missing data:

1. **Missing gebaeudedetails1:** Shows "Nicht verfügbar" for missing values
2. **Invalid numbers:** Calculation is skipped, no area is calculated
3. **Zero/negative values:** Calculation is skipped for safety

## User Interface

The calculator displays:

1. **Read-only building data:**
   - Anzahl Geschosse (from gebaeudedetails1)
   - Raumhöhe (from gebaeudedetails1)

2. **User input:**
   - Breite der Wand (Wall width in meters)

3. **Calculated result:**
   - Berechnete Fläche in m² (Calculated area)
   - Calculation formula shown to user

4. **Helper message:**
   - Instructions to enter building data in Part 1 if missing

## Testing

The implementation includes comprehensive tests:

- **Unit tests:** Mathematical calculations (`WallAreaCalculation.test.tsx`)
- **Integration tests:** Query patterns (`GebaeudeformDataQuery.test.tsx`)
- **Error handling:** Missing data scenarios
- **Localization:** German decimal format handling

## Benefits

1. **Restored Functionality:** WallAreaCalculator now works as intended
2. **Consistent Pattern:** Follows same approach as working pages
3. **Automatic Calculations:** Real-time area calculation as user types
4. **Data Integrity:** Uses previously entered building dimensions
5. **User Experience:** Clear display of calculation formula and inputs

## Future Considerations

- Consider adding TypeScript interfaces to replace `any` types
- Potential to cache gebaeudedetails1 data across pages
- Could extend to support more complex building shapes
- May benefit from validation of building dimension ranges