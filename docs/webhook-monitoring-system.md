# Webhook Monitoring System

## Overview

This comprehensive webhook monitoring system addresses the critical issue where Supabase's "Verify JWT with legacy secret" setting breaks Stripe webhooks, causing payment processing failures.

## The Problem

When Supabase's "Verify JWT with legacy secret" setting is enabled:
- ✅ **User-facing Edge Functions** work correctly (they receive JWT tokens from authenticated users)
- ❌ **Stripe webhooks FAIL** with 401 errors (<PERSON><PERSON> doesn't send JWT tokens)
- 💰 **Payments are not processed** (certificates remain unpaid despite successful Stripe checkout)
- 🔇 **Failures are silent** (no obvious error indicators)

## The Solution

### 1. **Security Assessment**
Your energy certificate application **does NOT need** "Verify JWT with legacy secret" enabled because:
- Stripe webhooks use signature verification (more secure than JWT for webhooks)
- User-facing functions already have proper JWT verification where needed
- The webhook function correctly uses `verify_jwt: false` in its configuration

### 2. **Monitoring & Alerting System**
The system includes:
- **Real-time health monitoring** of webhook accessibility
- **Automatic detection** of JWT verification issues
- **Email alerts** when problems are detected
- **Admin dashboard integration** for visual monitoring
- **Historical tracking** of webhook health

## Components

### Database Tables
- `webhook_health_checks` - Stores health check results
- `webhook_health_status` (view) - Current health status
- Functions: `get_latest_webhook_health()`, `are_webhooks_healthy()`

### Edge Functions
- `webhook-monitor` - Performs health checks and sends alerts
- `scheduled-webhook-monitor` - Automated health checks via cron
- `stripe-webhook` - Your existing webhook (already properly configured)

### Frontend Components
- `WebhookHealthMonitor` - React component for admin dashboard
- Integrated into `AdminPage` for real-time monitoring

## Deployment

### Quick Deployment
```bash
# Make the script executable
chmod +x scripts/deploy-webhook-monitoring.sh

# Run the deployment script
./scripts/deploy-webhook-monitoring.sh
```

### Manual Deployment
```bash
# 1. Run database migrations
supabase db push

# 2. Deploy Edge Functions
supabase functions deploy webhook-monitor --no-verify-jwt
supabase functions deploy scheduled-webhook-monitor --no-verify-jwt

# 3. Set environment variables
supabase secrets set RESEND_API_KEY=your_resend_api_key
supabase secrets set ADMIN_EMAIL=<EMAIL>
supabase secrets set FROM_EMAIL=<EMAIL>
```

## Configuration

### Required Environment Variables
```bash
RESEND_API_KEY=re_xxx          # For sending alert emails
ADMIN_EMAIL=<EMAIL>   # Where to send alerts
FROM_EMAIL=<EMAIL>   # Email sender address
```

### Supabase Settings
**CRITICAL**: Ensure "Verify JWT with legacy secret" is **DISABLED**:
1. Go to Supabase Dashboard → Settings → API
2. Find "Verify JWT with legacy secret"
3. Ensure it's **OFF** (unchecked)

## Monitoring

### Admin Dashboard
- Navigate to `/admin` in your application
- View real-time webhook health status
- See historical health checks
- Get immediate alerts for critical issues

### Health Status Levels
- 🟢 **HEALTHY** - All systems operational
- 🟡 **WARNING** - Multiple webhook failures detected
- 🔴 **CRITICAL** - JWT verification enabled or webhook inaccessible

### Automated Monitoring
Set up a cron job for continuous monitoring:
```bash
# Every 15 minutes
*/15 * * * * curl -X POST https://your-project.supabase.co/functions/v1/scheduled-webhook-monitor
```

## Alert Types

### Critical Alerts
- **JWT Verification Enabled** - Immediate action required
- **Webhook Function Inaccessible** - System failure

### Warning Alerts
- **Multiple Webhook Failures** - Investigate payment issues
- **No Recent Successful Webhooks** - Potential system degradation

## Troubleshooting

### Common Issues

#### 1. Webhooks Failing with 401 Errors
**Cause**: JWT verification is enabled
**Solution**: 
1. Go to Supabase Dashboard → Settings → API
2. Disable "Verify JWT with legacy secret"
3. Verify fix by running manual health check

#### 2. No Health Check Data
**Cause**: Functions not deployed or environment variables missing
**Solution**:
```bash
# Redeploy functions
supabase functions deploy webhook-monitor --no-verify-jwt

# Check environment variables
supabase secrets list
```

#### 3. Email Alerts Not Working
**Cause**: Missing or incorrect Resend configuration
**Solution**:
```bash
# Verify Resend API key
supabase secrets set RESEND_API_KEY=your_correct_key

# Test email function manually
curl -X POST https://your-project.supabase.co/functions/v1/webhook-monitor \
  -H "Authorization: Bearer your_service_role_key"
```

#### 4. Admin Dashboard Not Loading Health Status
**Cause**: Database permissions or missing functions
**Solution**:
```bash
# Run migrations again
supabase db push

# Check function exists
supabase db diff
```

### Manual Testing

#### Test Webhook Accessibility
```bash
# Should return 400 (bad signature), NOT 401 (unauthorized)
curl -X POST https://your-project.supabase.co/functions/v1/stripe-webhook \
  -H "Content-Type: application/json" \
  -H "Stripe-Signature: test" \
  -d '{"test": true}'
```

#### Test Health Monitor
```bash
# Should return health status
curl -X POST https://your-project.supabase.co/functions/v1/webhook-monitor \
  -H "Authorization: Bearer your_service_role_key"
```

## Security Considerations

### Why JWT Verification Should Be Disabled for Webhooks
1. **Webhooks come from Stripe, not users** - No JWT tokens available
2. **Stripe signature verification is more secure** - Cryptographic proof of authenticity
3. **User functions still protected** - JWT verification remains on user-facing functions

### Proper Security Model
- ✅ **User functions**: JWT verification enabled
- ✅ **Webhooks**: Signature verification (no JWT)
- ✅ **Admin functions**: JWT + role verification
- ✅ **Public functions**: No authentication required

## Maintenance

### Regular Tasks
- Monitor webhook health status weekly
- Review failed webhook logs monthly
- Test alert system quarterly
- Update monitoring thresholds as needed

### Health Check Frequency
- **Real-time**: Admin dashboard updates every 30 seconds
- **Automated**: Scheduled checks every 15 minutes
- **Manual**: On-demand via admin dashboard

## Support

### Getting Help
1. Check the admin dashboard for current status
2. Review recent health check logs
3. Verify Supabase settings match recommendations
4. Test webhook accessibility manually

### Emergency Response
If webhooks are failing:
1. **Immediate**: Disable "Verify JWT with legacy secret"
2. **Verify**: Run manual health check
3. **Monitor**: Watch for successful webhook processing
4. **Investigate**: Review failed webhook logs for root cause

## Success Metrics

### Healthy System Indicators
- ✅ Webhook health status: HEALTHY
- ✅ JWT verification: DISABLED
- ✅ Recent successful webhooks within last hour
- ✅ Zero failed webhooks in last 24 hours
- ✅ Alert system responsive

### Performance Targets
- **Webhook success rate**: >99%
- **Alert response time**: <5 minutes
- **Health check frequency**: Every 15 minutes
- **False positive rate**: <1%
