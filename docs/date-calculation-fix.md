# Energy Consumption Date Calculation Fix

## Problem Description

The original implementation in `VerbrauchPage.tsx` had a logic error where entering a start date would calculate future consumption periods instead of past periods. Energy consumption data should always represent historical data, never future dates.

## Original (Incorrect) Behavior

- User enters "2024-01-15" as start date
- System calculated:
  - Period 1: 2024-01-15 to 2025-01-15 (future!)
  - Period 2: 2023-01-15 to 2024-01-15
  - Period 3: 2022-01-15 to 2023-01-15

## Fixed (Correct) Behavior

- User enters "2024-01-15" as the **end date** of the most recent consumption period
- System calculates backwards:
  - Period 1 (most recent): 2023-01-16 to 2024-01-15
  - Period 2: 2022-01-16 to 2023-01-15  
  - Period 3 (oldest): 2021-01-16 to 2022-01-15

## Implementation Changes

### 1. Updated Date Calculation Function

```typescript
// OLD: calculateAllDatesFromStartDate
const calculateAllDatesFromEndDate = (endDate: string): {
  jahr1Von: string;
  jahr1Bis: string;
  jahr2Von: string;
  jahr2Bis: string;
  jahr3Von: string;
  jahr3Bis: string;
} => {
  // The input date is the END of the most recent consumption period (Jahr 1)
  // Calculate three consecutive 12-month periods going backwards
  const jahr1Bis = endDate; // Most recent period ends on the input date
  const jahr1Von = calculateDateWithYearOffset(endDate, -1, 1); // Start 1 year before, +1 day
  const jahr2Bis = calculateDateWithYearOffset(endDate, -1); // Second period ends 1 year before
  const jahr2Von = calculateDateWithYearOffset(endDate, -2, 1); // Start 2 years before, +1 day
  const jahr3Bis = calculateDateWithYearOffset(endDate, -2); // Third period ends 2 years before
  const jahr3Von = calculateDateWithYearOffset(endDate, -3, 1); // Start 3 years before, +1 day

  return {
    jahr1Von,
    jahr1Bis,
    jahr2Von,
    jahr2Bis,
    jahr3Von,
    jahr3Bis
  };
};
```

### 2. Enhanced Utility Function

```typescript
// Enhanced to support day offset for consecutive periods
const calculateDateWithYearOffset = (dateString: string, yearOffset: number, dayOffset: number = 0): string => {
  if (!dateString) return '';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';

  // Add or subtract the specified number of years
  date.setFullYear(date.getFullYear() + yearOffset);
  
  // Add or subtract the specified number of days
  if (dayOffset !== 0) {
    date.setDate(date.getDate() + dayOffset);
  }

  // Return in YYYY-MM-DD format for date inputs
  return date.toISOString().split('T')[0];
};
```

### 3. Updated User Interface

- Changed the callback from "Zeitraum von" to "Zeitraum bis" field
- Updated help text to explain backward calculation
- Added "(aktuellster Zeitraum)" label to clarify which field to use

### 4. Updated Form Logic

```typescript
const updateCalculatedDates = (endDate: string, prefix: 'ETr1' | 'ETr2' | 'ETr3') => {
  if (!endDate) return;

  const calculatedDates = calculateAllDatesFromEndDate(endDate);

  // Now updates ALL date fields including Jahr1_von
  form.setFieldValue(`${prefix}_Jahr1_von`, calculatedDates.jahr1Von);
  form.setFieldValue(`${prefix}_Jahr1_bis`, calculatedDates.jahr1Bis);
  form.setFieldValue(`${prefix}_Jahr2_von`, calculatedDates.jahr2Von);
  form.setFieldValue(`${prefix}_Jahr2_bis`, calculatedDates.jahr2Bis);
  form.setFieldValue(`${prefix}_Jahr3_von`, calculatedDates.jahr3Von);
  form.setFieldValue(`${prefix}_Jahr3_bis`, calculatedDates.jahr3Bis);
};
```

## Key Benefits

1. **Correct Historical Data**: All consumption periods are now in the past
2. **Consecutive Periods**: No gaps between periods (each period ends one day before the next starts)
3. **User-Friendly**: Clear labeling and help text guide users to enter the correct date
4. **Consistent Logic**: Applied to all three energy carriers (ETr1, ETr2, ETr3)

## Testing

Comprehensive tests verify:
- Backward date calculation logic
- Consecutive period creation with no gaps
- All periods are in the past relative to current date
- Proper handling of edge cases (empty input, invalid dates)

The fix ensures that energy consumption data entry follows the correct temporal logic for historical consumption reporting.
