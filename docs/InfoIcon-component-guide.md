# InfoIcon Component Guide

## Overview

The `InfoIcon` component provides a reusable, accessible way to display contextual help information in form fields and other UI elements. It displays an information icon that shows a tooltip with helpful text when hovered or clicked.

## Features

- **Accessible**: Full keyboard navigation support (Tab, Enter, Space, Escape)
- **Screen Reader Compatible**: Proper ARIA attributes for assistive technologies
- **Responsive**: Automatic tooltip positioning to prevent overflow with intelligent width adaptation
- **Interactive**: Supports both hover and click interactions
- **Customizable**: Multiple sizes and custom styling options
- **Consistent**: Integrates seamlessly with existing form components
- **Optimized Layout**: Wide tooltips with responsive breakpoints for better readability
- **Smart Typography**: Improved line spacing, word wrapping, and hyphenation

## Basic Usage

### Standalone InfoIcon

```tsx
import { InfoIcon } from '../components/ui/InfoIcon';

<InfoIcon 
  helpText="This is helpful information about this field"
  size="md"
/>
```

### With Form Fields

The InfoIcon is automatically integrated into all enhanced form field components:

```tsx
import { EnhancedFormField } from '../components/ui/EnhancedFormField';

<EnhancedFormField
  name="wohneinheiten"
  label="Anzahl Wohneinheiten"
  placeholder="z.B. 3"
  required={true}
  form={form}
  infoText="Eine Wohneinheit ist eine aus mehreren Räumen bestehende Einheit..."
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `helpText` | `string` | - | **Required.** The help text to display in the tooltip |
| `className` | `string` | `''` | Optional CSS classes for custom styling |
| `position` | `'top' \| 'bottom' \| 'left' \| 'right'` | `'top'` | Preferred tooltip position (auto-adjusts if needed) |
| `size` | `'sm' \| 'md' \| 'lg'` | `'lg'` | Size of the info icon |

## Integration with Form Components

### EnhancedFormField

```tsx
<EnhancedFormField
  name="fieldName"
  label="Field Label"
  form={form}
  infoText="Helpful information about this field"
  // ... other props
/>
```

### EnhancedSelectField

```tsx
<EnhancedSelectField
  name="fieldName"
  label="Field Label"
  options={options}
  form={form}
  infoText="Helpful information about this dropdown"
  // ... other props
/>
```

### CheckboxField

```tsx
<CheckboxField
  name="fieldName"
  label="Field Label"
  form={form}
  infoText="Helpful information about this checkbox"
  // ... other props
/>
```

## Accessibility Features

### Keyboard Navigation
- **Tab**: Focus the info icon
- **Enter/Space**: Show/hide tooltip
- **Escape**: Hide tooltip

### Screen Reader Support
- Proper ARIA labels and descriptions
- Dynamic `aria-describedby` linking to tooltip content
- Semantic HTML structure

### Visual Indicators
- Focus ring for keyboard navigation
- Hover states for mouse interaction
- Clear visual hierarchy

## Examples

### Different Sizes

```tsx
<div className="flex items-center gap-4">
  <InfoIcon helpText="Small icon" size="sm" />
  <InfoIcon helpText="Medium icon" size="md" />
  <InfoIcon helpText="Large icon" size="lg" />
</div>
```

### Custom Positioning

```tsx
<InfoIcon 
  helpText="This tooltip appears below the icon"
  position="bottom"
/>
```

### Real-World Example: Wohneinheiten Field

```tsx
<EnhancedFormField
  name="Wohneinheiten"
  label="Anzahl Wohneinheiten"
  placeholder="z.B. 3"
  required={true}
  form={form}
  infoText="Eine Wohneinheit ist eine aus mehreren Räumen bestehende Einheit, in der ein selbstständiger Haushalt geführt werden kann. Die Räume müssen baulich von anderen Bereichen getrennt sein, einen eigenen Zugang besitzen und über die für die Haushaltsführung erforderlichen Räumlichkeiten (Küche/Kochecke, Dusche/Bad, Toilette) verfügen. Dies kann ein einzelnes Haus oder eine einzelne Wohnung innerhalb eines Wohnhauses sein."
/>
```

## Styling

The InfoIcon uses Tailwind CSS classes and follows the application's design system:

- **Colors**: Gray-400 default, Gray-600 on hover, Blue-500 focus ring
- **Sizes**:
  - Small: 16px × 16px
  - Medium: 24px × 24px
  - Large: 32px × 32px
- **Tooltip**: Enhanced layout with responsive width and improved readability
  - Background: Dark gray (gray-800) with white text
  - Width: Responsive from max-w-sm (384px) to lg:max-w-xl (576px)
  - Minimum width: 16rem (256px) for better content display
  - Padding: Enhanced spacing (px-4 py-3) for improved readability
  - Typography: Line height 1.5, relaxed leading, automatic word wrapping
  - Corners: Rounded (rounded-lg) with enhanced shadow (shadow-xl)

## Best Practices

### Content Guidelines
- Keep help text concise but informative
- Use clear, non-technical language when possible
- Provide context-specific information
- Include examples when helpful

### Usage Guidelines
- Use for complex or unfamiliar form fields
- Don't overuse - only add where genuinely helpful
- Place consistently next to field labels
- Test with screen readers and keyboard navigation

### Technical Guidelines
- Always provide meaningful `helpText`
- Use appropriate size for the context
- Test tooltip positioning on different screen sizes
- Ensure sufficient color contrast

## Recent Improvements

### Enhanced Tooltip Layout (v2.0)

The InfoIcon component has been significantly improved to provide better readability and user experience:

#### Width Optimization
- **Responsive breakpoints**: Tooltips now use responsive width classes from `max-w-sm` (384px) on mobile to `lg:max-w-xl` (576px) on large screens
- **Minimum width**: Set to 16rem (256px) to prevent overly narrow tooltips
- **Better space utilization**: Tooltips make better use of available horizontal screen space

#### Typography Enhancements
- **Improved line spacing**: Line height set to 1.5 for better readability
- **Relaxed leading**: Additional spacing between lines with `leading-relaxed`
- **Smart word wrapping**: Automatic word breaking and hyphenation for long text

#### Layout Improvements
- **Enhanced padding**: Increased from `px-3 py-2` to `px-4 py-3` for better content breathing room
- **Improved shadows**: Upgraded from `shadow-lg` to `shadow-xl` for better visual hierarchy
- **Rounded corners**: Enhanced from `rounded-md` to `rounded-lg` for modern appearance

#### Smart Positioning
- **Intelligent overflow detection**: Improved algorithm that considers viewport margins
- **Adaptive positioning**: Better fallback positioning when preferred position would cause overflow
- **Responsive awareness**: Position calculations account for different screen sizes

## Testing

The InfoIcon component includes comprehensive tests covering:

- Rendering and basic functionality
- Hover and click interactions
- Keyboard navigation (Tab, Enter, Space, Escape)
- Accessibility features and screen reader compatibility
- Different sizes and configurations
- **New**: Responsive width classes and improved layout
- **New**: Long text content handling and readability
- **New**: Enhanced tooltip styling verification

Run tests with:
```bash
npm test InfoIcon.test.tsx
```

## Browser Support

The InfoIcon component works in all modern browsers and follows web accessibility standards (WCAG 2.1 AA).
