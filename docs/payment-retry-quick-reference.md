# Payment Retry - Quick Reference Guide

## Status Overview

| Status | Retry? | Button Color | Button Text | User Message |
|--------|--------|--------------|-------------|--------------|
| `zusammenfassung` | ✅ | Green | "Energieausweis kaufen" | Standard form completion |
| `payment_initiated` | ⏳ | Blue | "Zahlung läuft..." | Payment in progress |
| `payment_complete` | 🚫 | Green | "Bezahlt & Abgeschlossen" | Success - no retry |
| `payment_failed` | ✅ | Orange | "Zahlung erneut versuchen" | Payment failed - retry available |
| `payment_expired` | ✅ | Orange | "Zahlung erneut versuchen" | Session expired - retry available |
| `payment_disputed` | ✅ | Orange | "Zahlung erneut versuchen" | Chargeback - retry available |

## Code Snippets

### Status Checks
```typescript
const isPaymentInProgress = status === 'payment_initiated';
const isPaymentFailed = ['payment_failed', 'payment_disputed', 'payment_expired'].includes(status);
const isPaid = status === 'payment_complete';
```

### Button Logic
```typescript
{isPaid ? (
  // Success state - no retry
) : isPaymentInProgress ? (
  // Loading state - payment in progress  
) : (
  // Payment/retry button
  <button className={isPaymentFailed ? 'bg-orange-600' : 'bg-green-600'}>
    {isPaymentFailed ? 'Zahlung erneut versuchen' : 'Energieausweis kaufen'}
  </button>
)}
```

### Status Transition Rules
```typescript
// Allow retry from failed states, block from success
if (currentStatus === 'payment_complete') {
  return null; // Block retry after success
}
// Allow all other transitions for retry
```

## Testing Checklist

- [ ] Normal payment flow works
- [ ] Payment failure shows retry button
- [ ] Session expiration shows retry button  
- [ ] Successful payment blocks retry
- [ ] Status messages are accurate
- [ ] Button colors are correct
- [ ] Retry functionality works
- [ ] Double payment prevention works

## Common Issues

**Issue**: Retry button not showing after failure
**Fix**: Check webhook status updates

**Issue**: Can retry after success
**Fix**: Verify `isPaid` logic

**Issue**: Wrong button color
**Fix**: Check `isPaymentFailed` condition

## Key Files

- `src/pages/erfassen/ZusammenfassungPage.tsx` - Main implementation
- `supabase/functions/stripe-webhook/index.ts` - Status updates
- `src/types/csv.ts` - Status type definitions

## Webhook Events → Status

- `checkout.session.completed` → `payment_complete` (no retry)
- `checkout.session.expired` → `payment_expired` (retry available)
- `charge.failed` → `payment_failed` (retry available)
- `charge.dispute.created` → `payment_disputed` (retry available)

## User Experience Flow

1. **Initial**: Green "Buy" button
2. **Processing**: Blue "Payment running..."
3. **Success**: Green "Paid & Complete" (final)
4. **Failure**: Orange "Retry Payment" + warning message
5. **Retry**: Back to step 2

## Security Notes

- ✅ Server-side status validation
- ✅ Prevent double charging
- ✅ Audit trail logging
- ✅ User ownership verification
- ✅ Session validation

## Monitoring

Watch for:
- Payment conversion rates
- Retry success rates  
- Failed payment recovery
- Double payment incidents
- Status transition errors

## Quick Debug

```bash
# Check certificate status
SELECT id, status, updated_at FROM energieausweise WHERE id = 'cert-id';

# Check payment attempts
SELECT * FROM payment_attempts WHERE certificate_id = 'cert-id';

# Check webhook events
SELECT * FROM stripe_webhook_events WHERE certificate_id = 'cert-id';
```

This quick reference provides immediate access to the most important information for developers working with the payment retry functionality.
