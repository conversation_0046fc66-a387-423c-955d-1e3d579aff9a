# Testing Framework and Validation Methodology

## Overview

This document provides comprehensive documentation for the testing framework and validation methodology used in the Energy Certificate Application, specifically focusing on the `GebaeudedetailsPage1.validation.test.ts` file as a reference implementation.

## Testing Framework Overview

### Vitest Integration

The project uses **Vitest** as the primary testing framework, which provides:

- **TypeScript Support**: Native TypeScript support without additional configuration
- **Vite Integration**: Seamless integration with the Vite build system
- **Jest-Compatible API**: Familiar `describe`, `test`, and `expect` syntax
- **Fast Execution**: Optimized for speed with hot module replacement
- **Modern Features**: ES modules support, async/await, and modern JavaScript features

### Configuration

The testing setup includes:

```typescript
// vite.config.ts
export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,           // Enable global test functions
    environment: 'jsdom',    // DOM environment for React components
    setupFiles: ['./src/test/setup.ts'], // Test setup file
  },
})
```

### Dependencies

Key testing dependencies:
- `vitest`: Core testing framework
- `@vitest/ui`: Optional UI for test visualization
- `jsdom`: DOM environment simulation
- `@testing-library/react`: React component testing utilities
- `@testing-library/jest-dom`: Additional DOM matchers

## Certificate Types and Validation Context

### Certificate Type System

The application supports three energy certificate types:

| Type | Full Name | Description | BedarfVerbrauch | nichtWohnGeb |
|------|-----------|-------------|-----------------|--------------|
| **WG/V** | Wohngebäude/Verbrauchsausweis | Residential Consumption Certificate | V | 0 |
| **WG/B** | Wohngebäude/Bedarfsausweis | Residential Demand Certificate | B | 0 |
| **NWG/V** | Nichtwohngebäude/Verbrauchsausweis | Non-Residential Consumption Certificate | V | 1 |

### Field Validation Rules by Certificate Type

Different certificate types require different sets of fields:

#### Common Fields (All Types)
- `Baujahr` (Construction Year)
- `Wohnfläche` (Living Area)
- `Wohneinheiten` (Housing Units)
- `baujahrHzErz` (Heating System Construction Year)

#### WG/B Specific Fields (Demand Certificates)
- `Raumhöhe` (Room Height) - Required
- `Volumen` (Building Volume) - Required  
- `Geschosse` (Number of Floors) - Required
- `anbauSituation` (Building Attachment Situation) - Required

#### NWG/V Specific Fields (Non-Residential)
- `Nutzung1_ID` (Usage Type ID) - Required
- `Nutzung1_Flaeche` (Usage Area) - Required

#### WG/V Specific Fields (Residential Consumption)
- `Keller_beheizt` (Heated Basement) - Optional

## Test File Structure Analysis

### File Organization

```
src/pages/erfassen/__tests__/
└── GebaeudedetailsPage1.validation.test.ts
```

### Test Structure Breakdown

#### 1. Schema Definition
```typescript
const gebaeudedetailsSchema = z.object({
  // Mirrors the actual component schema
  anbauSituation: z.enum(['0', '1', '2']).optional().default('0'),
  // ... other fields
});
```

#### 2. Mock Validation Function
```typescript
const validateAnbauSituation = (
  values: GebaeudedetailsFormValues,
  certificateType: 'WG/V' | 'WG/B' | 'NWG/V'
): string | null => {
  // Mimics component validation logic
};
```

#### 3. Test Data Setup
```typescript
const baseValidData: GebaeudedetailsFormValues = {
  // Complete valid data object for testing
};
```

## Test Cases Documentation

### Main Test Suite: `GebaeudedetailsPage1 anbauSituation Validation`

This suite focuses on testing the `anbauSituation` field validation logic.

#### Test Group 1: `anbauSituation field validation`

**Purpose**: Validate that the `anbauSituation` field behaves correctly for different certificate types and input values.

##### Test Case 1: Valid Values for WG/B Certificate
```typescript
test('should accept "0" (Freistehend) as valid value for WG/B certificate')
test('should accept "1" (Einseitig angebaut) as valid value for WG/B certificate') 
test('should accept "2" (Zweiseitig angebaut) as valid value for WG/B certificate')
```

**Validation Logic**:
1. Schema validation with `gebaeudedetailsSchema.safeParse()`
2. Custom validation with `validateAnbauSituation()`
3. Expects both validations to pass

##### Test Case 2: Required Field Validation for WG/B
```typescript
test('should fail validation when anbauSituation is undefined for WG/B certificate')
```

**Key Testing Pattern**:
- Tests edge case where field is manually set to `undefined`
- Schema passes due to default value, but custom validation fails
- Demonstrates two-layer validation approach

##### Test Case 3: Optional Field for Other Certificate Types
```typescript
test('should not require anbauSituation for non-WG/B certificate types')
```

**Validation Logic**:
- Tests both WG/V and NWG/V certificate types
- Confirms field is optional for these types
- Validates conditional requirement logic

##### Test Case 4: Default Value Behavior
```typescript
test('should use default value "0" when field is not provided')
```

**Purpose**: Ensures Zod schema default values work correctly

#### Test Group 2: `Schema validation edge cases`

##### Test Case 5: Invalid Enum Values
```typescript
test('should reject invalid enum values')
```

**Purpose**: Validates that schema rejects values outside the defined enum

## Testing Patterns and Methodologies

### 1. Schema Validation Testing with Zod

**Pattern**: Two-layer validation approach
```typescript
// Layer 1: Zod schema validation
const result = gebaeudedetailsSchema.safeParse(testData);
expect(result.success).toBe(true);

// Layer 2: Custom business logic validation
if (result.success) {
  const validationError = validateAnbauSituation(result.data, 'WG/B');
  expect(validationError).toBeNull();
}
```

**Benefits**:
- Separates data structure validation from business logic
- Allows for complex conditional validation rules
- Provides clear error messages for different validation failures

### 2. Mock Data Creation and Manipulation

**Base Data Pattern**:
```typescript
const baseValidData: GebaeudedetailsFormValues = {
  // Complete valid object
};

// Test-specific modifications
const testData = { ...baseValidData, anbauSituation: '1' as const };
```

**Benefits**:
- Ensures consistent test data
- Reduces test setup complexity
- Makes test intentions clear through modifications

### 3. Conditional Validation Based on Certificate Types

**Pattern**: Certificate type as test parameter
```typescript
const validateAnbauSituation = (
  values: GebaeudedetailsFormValues,
  certificateType: 'WG/V' | 'WG/B' | 'NWG/V'
): string | null => {
  if (certificateType === 'WG/B') {
    // WG/B specific validation
  }
  return null;
};
```

**Testing Approach**:
- Test each certificate type separately
- Verify conditional logic works correctly
- Ensure non-applicable types are handled properly

### 4. Edge Case Testing

**Undefined/Null Handling**:
```typescript
// Test manual undefined assignment
const modifiedData = { ...result.data, anbauSituation: undefined as any };
```

**Invalid Value Testing**:
```typescript
// Test values outside enum range
anbauSituation: '3', // Invalid value
```

## Visual Diagrams

### Test Execution Flow

```mermaid
graph TD
    A[Test Suite Start] --> B[Schema Definition]
    B --> C[Mock Function Setup]
    C --> D[Base Test Data Creation]
    D --> E[Test Group 1: Field Validation]
    E --> F[Valid Values Tests]
    E --> G[Required Field Tests]
    E --> H[Optional Field Tests]
    E --> I[Default Value Tests]
    D --> J[Test Group 2: Edge Cases]
    J --> K[Invalid Enum Tests]
    F --> L[Test Complete]
    G --> L
    H --> L
    I --> L
    K --> L
```

### Certificate Type Validation Rules

```mermaid
graph LR
    A[Certificate Type] --> B{Type Check}
    B -->|WG/V| C[Residential Consumption]
    B -->|WG/B| D[Residential Demand]
    B -->|NWG/V| E[Non-Residential Consumption]
    
    C --> F[anbauSituation: Optional]
    D --> G[anbauSituation: Required]
    E --> H[anbauSituation: Optional]
    
    G --> I[Values: 0,1,2]
    I --> J[0: Freistehend]
    I --> K[1: Einseitig angebaut]
    I --> L[2: Zweiseitig angebaut]
```

### Validation Logic Decision Tree

```mermaid
graph TD
    A[Input Data] --> B[Zod Schema Validation]
    B -->|Fail| C[Schema Error]
    B -->|Pass| D{Certificate Type}
    D -->|WG/B| E{anbauSituation defined?}
    D -->|WG/V or NWG/V| F[Validation Pass]
    E -->|No| G[Validation Error]
    E -->|Yes| H{Valid enum value?}
    H -->|No| I[Schema Error]
    H -->|Yes| J[Validation Pass]
```

## Running Tests

### Command Line Interface

```bash
# Run all tests
npm test

# Run specific test file
npm test src/pages/erfassen/__tests__/GebaeudedetailsPage1.validation.test.ts

# Run tests with UI
npm run test:ui

# Run tests once (CI mode)
npm run test:run
```

### Test Output Example

```
✓ GebaeudedetailsPage1 anbauSituation Validation (7 tests) 4ms
  ✓ should accept "0" (Freistehend) as valid value for WG/B certificate 1ms
  ✓ should accept "1" (Einseitig angebaut) as valid value for WG/B certificate 1ms
  ✓ should accept "2" (Zweiseitig angebaut) as valid value for WG/B certificate 0ms
  ✓ should fail validation when anbauSituation is undefined for WG/B certificate 0ms
  ✓ should not require anbauSituation for non-WG/B certificate types 0ms
  ✓ should use default value "0" when field is not provided 0ms
  ✓ should reject invalid enum values 0ms
```

## Best Practices for Validation Testing

### 1. Test Structure
- Group related tests using `describe` blocks
- Use descriptive test names that explain the expected behavior
- Test both positive and negative cases

### 2. Data Management
- Create reusable base data objects
- Use type assertions for test-specific modifications
- Keep test data minimal but complete

### 3. Validation Logic
- Test schema validation separately from business logic
- Verify conditional requirements based on certificate types
- Test edge cases and boundary conditions

### 4. Error Handling
- Test both validation success and failure scenarios
- Verify error messages are appropriate and helpful
- Ensure graceful handling of undefined/null values

## Extending the Testing Framework

### Adding New Validation Tests

1. **Create Test File**: Follow naming convention `ComponentName.validation.test.ts`
2. **Define Schema**: Mirror the component's Zod schema
3. **Create Mock Functions**: Replicate component validation logic
4. **Structure Tests**: Use consistent describe/test organization
5. **Test All Scenarios**: Cover all certificate types and edge cases

### Integration with Component Testing

This validation testing approach can be extended to include:
- Component rendering tests with `@testing-library/react`
- User interaction testing
- Form submission testing
- Integration tests with Supabase

## Conclusion

The validation testing framework provides a robust foundation for ensuring data integrity across different certificate types. The two-layer validation approach (schema + business logic) combined with comprehensive test coverage ensures that the application handles all scenarios correctly while maintaining code quality and reliability.
