# Wall Area Calculation Enhancement

## Overview

This document describes the simplified wall area calculation functionality implemented in the energy certificate application. The enhancement automatically uses previously entered building data to calculate wall areas, requiring only wall width input from the user.

## Implementation Details

### Location
- **File**: `src/pages/erfassen/GebaeudedetailsPage2.tsx`
- **Component**: `WallAreaCalculator` (enhanced component)
- **Scope**: Only applies to wall components (`type === 'wand'`) in the `BauteilField` component

### Functionality

#### User Input Required
The simplified calculation requires only one input field:

1. **Breite der Wand** (Wall Width)
   - Input type: Text field
   - Unit: Meters
   - Placeholder: "z.B. 12,5"
   - Supports German decimal format (comma as decimal separator)

#### Automatic Value Retrieval
The component automatically retrieves previously entered values from building data:

1. **Anzahl Geschosse** (Number of Floors)
   - Source: `Geschosse` field from GebaeudedetailsPage1.tsx
   - Display: Read-only information box
   - Used automatically in calculations

2. **Raumhöhe** (Room Height)
   - Source: `Raumhöhe` field from GebaeudedetailsPage1.tsx
   - Description: "Durchschnittliche Raumhöhe in m"
   - Display: Read-only information box
   - Used automatically in calculations instead of hardcoded 2.75m

#### Calculation Formula
```
Total Area = Width × Number of Floors × Room Height
```

Where:
- **Width**: User-provided wall width in meters
- **Number of Floors**: Automatically retrieved from `Geschosse` field
- **Room Height**: Automatically retrieved from `Raumhöhe` field

#### Real-time Calculation
- The calculation updates automatically as the user types the wall width
- Previously entered values (floors and room height) are displayed as read-only information
- Results are displayed in a read-only field showing the calculated area
- A formula explanation shows all three values used in the calculation
- German decimal formatting is used (comma as decimal separator)
- Warning message displayed if required building data values are missing

### Technical Implementation

#### Component Structure
```typescript
const WallAreaCalculator = ({
  flaecheField,
  wallWidthField,
  index
}: {
  flaecheField: any;
  wallWidthField: any;
  index: number;
}) => {
  const [calculatedArea, setCalculatedArea] = useState<string>('');

  // Get previously entered values from gebaeudedetails1
  const gebaeudedetails1Data = existingData?.gebaeudedetails1 as any;
  const geschosseValue = gebaeudedetails1Data?.Geschosse || '';
  const raumhoeheValue = gebaeudedetails1Data?.Raumhöhe || '';

  // Real-time calculation effect using actual room height
  useEffect(() => {
    // Calculation logic using retrieved building data
  }, [wallWidthField.state.value, geschosseValue, raumhoeheValue, flaecheField]);
}
```

#### Key Features
1. **German Decimal Format Support**: Converts comma-separated decimals to dots for calculation
2. **Real-time Updates**: Uses `useEffect` to recalculate when wall width changes
3. **Form Integration**: Updates the main form field (`flaecheField`) automatically
4. **Automatic Data Retrieval**: Fetches building data from previously completed forms
5. **Validation**: Handles invalid inputs gracefully and shows warnings for missing data
6. **User Feedback**: Shows calculation formula with all actual values used
7. **Simplified UX**: Eliminates redundant data entry by reusing existing building information

### Database Compatibility

#### Schema Preservation
- **No changes** to existing database schema
- **No changes** to form validation schema
- Only the final calculated `flaeche` value is stored in the database
- Intermediate values (width, levels) are not persisted

#### Data Flow
1. User enters width and number of levels
2. Component calculates area using the formula
3. Calculated area is automatically set in the form field
4. Form submission stores only the final `flaeche` value
5. Database receives the same data structure as before

### User Experience

#### Visual Design
- Clean, organized layout with labeled input fields
- Grid layout for responsive design
- Read-only result field with gray background
- Formula explanation for transparency
- German language labels throughout

#### Validation & Error Handling
- Handles invalid numeric inputs
- Clears calculation when inputs are invalid
- Preserves existing form validation for the area field
- Shows validation errors from the main form field

### Testing

#### Test Coverage
The implementation includes comprehensive tests covering:
- Basic calculation scenarios
- Decimal value handling
- German decimal format conversion
- Edge cases (zero, negative, invalid inputs)
- Single level calculations
- Formatting and precision

#### Test File
- **Location**: `src/tests/WallAreaCalculation.test.tsx`
- **Framework**: Vitest
- **Coverage**: 7 test cases covering various scenarios

### Backward Compatibility

#### Existing Functionality
- All existing wall area input functionality is preserved
- Non-wall components (floors, roofs) continue to use the original area input
- Form submission and data processing remain unchanged
- CSV export functionality unaffected

#### Migration
- No migration required
- Enhancement is additive only
- Existing data remains valid and accessible

### Usage Instructions

#### For Users
1. Navigate to "Gebäudedetails erfassen (Teil 2)" page
2. Ensure building data (floors and room height) was entered in "Gebäudedetails erfassen (Teil 1)"
3. In the "Außenwände" section, each wall now shows:
   - Read-only display of number of floors and room height from building data
   - Width input field (in meters) - the only required user input
   - Calculated area display (read-only)
4. Enter only the wall width - floors and room height are used automatically
5. The area is calculated automatically using actual building dimensions
6. Continue with form submission as usual

#### For Developers
- The `WallAreaCalculator` component requires `flaecheField`, `wallWidthField`, and `index` props
- Component automatically accesses `existingData?.gebaeudedetails1` for building dimensions
- Component handles all state management internally
- No additional dependencies required
- Gracefully handles missing building data with user-friendly warnings

### Future Enhancements

#### Potential Improvements
1. **Room Height Override**: Allow users to temporarily override room height for specific walls
2. **Multiple Wall Sections**: Support for walls with different heights within the same building
3. **Area Validation**: Add reasonable bounds checking for calculated areas
4. **Building Data Validation**: Ensure required building data is complete before wall calculations
5. **Unit Conversion**: Support for different measurement units
6. **Calculation History**: Show previous calculations for reference

#### Considerations
- Maintain database schema compatibility
- Preserve existing user workflows and the simplified UX approach
- Consider internationalization for other languages
- Evaluate performance impact for large numbers of walls
- Ensure building data dependencies are clearly communicated to users
