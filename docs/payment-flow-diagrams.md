# Payment Flow with Retry Functionality - Complete Documentation

## Enhanced Payment Lifecycle with Retry Capabilities

```mermaid
graph TD
    A[zusammenfassung] --> B[User clicks Pay Button]
    B --> C[Status: payment_initiated]
    C --> D[Create Stripe Checkout Session]
    D --> E[Redirect to Stripe Checkout]

    E --> F{Payment Outcome}
    F -->|Success| G[Status: payment_complete]
    F -->|Failed| H[Status: payment_failed]
    F -->|Expired 24h| I[Status: payment_expired]
    F -->|Disputed| J[Status: payment_disputed]
    F -->|User Cancels| K[Return to Summary Page]

    G --> L[✅ Payment Complete - No Retry]
    H --> M[🔄 Retry Available]
    I --> M
    J --> M
    K --> N[Status remains: zusammenfassung]

    M --> O[User sees Retry Button]
    O --> P[User clicks Retry]
    P --> C

    N --> Q[User can retry immediately]
    Q --> B

    L --> R[Send Success Email]
    M --> S[Show Failure Message + Retry Option]

    R --> T[Admin Dashboard Updated]
    S --> T
```

## Payment Status Transitions and Retry Logic

```mermaid
graph TD
    A[Certificate Created] --> B[Status: objektdaten/gebaeudedetails1/etc.]
    B --> C[User completes forms]
    C --> D[Status: zusammenfassung]

    D --> E{User Action}
    E -->|Clicks Pay| F[Status: payment_initiated]
    E -->|Edits Data| G[Status remains: zusammenfassung]

    F --> H[Stripe Checkout Process]
    H --> I{Payment Result}

    I -->|✅ Success| J[Status: payment_complete]
    I -->|❌ Failed| K[Status: payment_failed]
    I -->|⏰ Expired| L[Status: payment_expired]
    I -->|⚠️ Disputed| M[Status: payment_disputed]

    J --> N[🚫 NO RETRY - Final State]
    K --> O[✅ RETRY ALLOWED]
    L --> O
    M --> O

    O --> P[Show Retry Button]
    P --> Q[User clicks Retry]
    Q --> F

    G --> R[User can start payment]
    R --> E

    N --> S[Certificate Complete]
    O --> T[User Guidance + Retry Option]
```

## Frontend Payment Button Logic

```mermaid
graph TD
    A[ZusammenfassungPage Loads] --> B[Check Certificate Status]

    B --> C{Status Check}
    C -->|payment_complete| D[Show: Bezahlt & Abgeschlossen]
    C -->|payment_initiated| E[Show: Zahlung läuft...]
    C -->|payment_failed| F[Show: Retry Button - Orange]
    C -->|payment_expired| F
    C -->|payment_disputed| F
    C -->|zusammenfassung| G[Show: Pay Button - Green]

    D --> H[No Action Available]
    E --> I[Show Loading State]
    F --> J[Button Text: Zahlung erneut versuchen]
    G --> K[Button Text: Energieausweis kaufen]

    J --> L[onClick: handleCheckout]
    K --> L

    L --> M[Update Status: payment_initiated]
    M --> N[Create Stripe Session]
    N --> O[Redirect to Stripe]
```

## Webhook Event Processing with Retry Support

```mermaid
graph TD
    A[Webhook Received] --> B[Verify Signature]
    B -->|Valid| C[Log Event to Database]
    B -->|Invalid| D[Return 400 Error]

    C --> E[Check Idempotency]
    E -->|Already Processed| F[Return 200 - Skipped]
    E -->|New Event| G[Extract Certificate ID]

    G --> H{Event Type}

    H -->|checkout.session.completed| I[Update: payment_complete]
    H -->|checkout.session.expired| J[Update: payment_expired]
    H -->|checkout.session.async_payment_failed| K[Update: payment_failed]
    H -->|charge.failed| L[Update: payment_failed]
    H -->|charge.dispute.created| M[Update: payment_disputed]
    H -->|payment_intent.payment_failed| N[Update: payment_failed]
    H -->|Other Events| O[Log Only]

    I --> P[🚫 Disable Retry - Final State]
    J --> Q[✅ Enable Retry - Show Button]
    K --> Q
    L --> Q
    M --> Q
    N --> Q

    P --> R[Send Success Notifications]
    Q --> S[Send Failure/Expiry Notifications]
    O --> T[Update Event Status: unhandled]

    R --> U[Update Event Status: handled]
    S --> U
    T --> V[Return 200 Success]
    U --> V
```

## Session Monitoring Architecture

```mermaid
graph TD
    A[Scheduled Function: check-abandoned-sessions] --> B[Query Unpaid Certificates]
    B --> C[Filter: 30min+ old sessions]
    C --> D[For Each Certificate]
    
    D --> E[Retrieve Stripe Session]
    E --> F{Session Status}
    
    F -->|expired| G[Update: expired]
    F -->|complete + paid| H[Update: paid]
    F -->|open + >30min| I[Update: abandoned]
    F -->|other| J[Update: abandoned]
    
    G --> K[Create/Update Payment Attempt]
    H --> K
    I --> K
    J --> K
    
    K --> L[Calculate Session Duration]
    L --> M[Log Abandonment Reason]
    M --> N[Update Analytics]
```

## Admin Dashboard Data Flow

```mermaid
graph TD
    A[Admin Dashboard Load] --> B[Query Payment Analytics]
    A --> C[Query Webhook Events]
    A --> D[Query Payment Attempts]
    
    B --> E[get_payment_analytics() Function]
    E --> F[Calculate Metrics]
    F --> G[Return JSON Response]
    
    C --> H[Group by Event Type & Status]
    H --> I[Calculate Counts & Latest Events]
    
    D --> J[Group by Attempt Status]
    J --> K[Calculate Avg Duration]
    
    G --> L[Display Analytics Cards]
    I --> M[Display Webhook Table]
    K --> N[Display Attempts Table]
    
    L --> O{Auto-refresh Enabled?}
    M --> O
    N --> O
    
    O -->|Yes| P[Set 30s Interval]
    P --> Q[Invalidate Queries]
    Q --> B
    
    O -->|No| R[Manual Refresh Only]
```

## Error Handling Decision Tree

```mermaid
graph TD
    A[Payment Error Occurs] --> B{Error Source}
    
    B -->|Stripe Webhook| C[Webhook Handler]
    B -->|Frontend| D[Frontend Error Handler]
    B -->|Session Timeout| E[Timeout Handler]
    
    C --> F{Event Type}
    F -->|charge.failed| G[Card/Payment Issue]
    F -->|session.expired| H[Timeout Issue]
    F -->|dispute.created| I[Chargeback Issue]
    
    D --> J{Error Code}
    J -->|card_declined| K[Card Declined Guidance]
    J -->|insufficient_funds| L[Insufficient Funds Guidance]
    J -->|processing_error| M[Technical Error Guidance]
    
    E --> N[Session Timeout Warning]
    N --> O[5min Warning → 30min Expiry]
    
    G --> P[Update Status: failed]
    H --> Q[Update Status: expired]
    I --> R[Update Status: disputed]
    K --> S[Show Card Troubleshooting]
    L --> T[Show Balance Troubleshooting]
    M --> U[Show Technical Troubleshooting]
    
    P --> V[Send Failure Email]
    Q --> W[Send Expiry Email]
    R --> X[Send Dispute Email]
    
    V --> Y[Log to payment_attempts]
    W --> Y
    X --> Y
    
    S --> Z[Log Cancellation Analytics]
    T --> Z
    U --> Z
```

## Database Relationship Diagram

```mermaid
erDiagram
    energieausweise ||--o{ payment_attempts : "has many"
    energieausweise ||--o{ stripe_webhook_events : "referenced by"
    energieausweise ||--o{ email_logs : "has many"
    
    energieausweise {
        uuid id PK
        uuid user_id FK
        text payment_status
        text stripe_checkout_session_id
        text order_number
        timestamp created_at
        timestamp updated_at
    }
    
    payment_attempts {
        uuid id PK
        uuid certificate_id FK
        text stripe_session_id
        text attempt_status
        text abandonment_reason
        text failure_reason
        integer session_duration_seconds
        timestamp created_at
        timestamp completed_at
    }
    
    stripe_webhook_events {
        uuid id PK
        text stripe_event_id UK
        text event_type
        uuid certificate_id FK
        text processing_status
        jsonb raw_event_data
        timestamp event_created_at
        timestamp created_at
    }
    
    email_logs {
        uuid id PK
        uuid certificate_id FK
        text recipient_email
        text email_type
        text status
        timestamp sent_at
        timestamp created_at
    }
```

## Security and Access Control Flow

```mermaid
graph TD
    A[User Request] --> B{Authentication Required?}
    
    B -->|Yes| C[Check JWT Token]
    B -->|No| D[Public Access]
    
    C --> E{Valid Token?}
    E -->|No| F[Return 401 Unauthorized]
    E -->|Yes| G[Extract User ID]
    
    G --> H{Admin Required?}
    H -->|Yes| I[Check User Role]
    H -->|No| J[Apply User RLS]
    
    I --> K{Is Admin?}
    K -->|No| L[Return 403 Forbidden]
    K -->|Yes| M[Full Access Granted]
    
    J --> N[Filter by user_id]
    N --> O[Return User Data Only]
    
    M --> P[Return All Data]
    D --> Q[Return Public Data]
    
    F --> R[Error Response]
    L --> R
    O --> S[Success Response]
    P --> S
    Q --> S
```

## Performance Monitoring Flow

```mermaid
graph TD
    A[System Monitoring] --> B[Webhook Performance]
    A --> C[Database Performance]
    A --> D[Frontend Performance]
    
    B --> E[Processing Time < 10s]
    B --> F[Success Rate > 99%]
    B --> G[Event Volume Tracking]
    
    C --> H[Query Response Time]
    C --> I[Connection Pool Usage]
    C --> J[Index Effectiveness]
    
    D --> K[Page Load Time]
    D --> L[API Response Time]
    D --> M[Auto-refresh Impact]
    
    E --> N{Threshold Exceeded?}
    F --> N
    G --> N
    H --> N
    I --> N
    J --> N
    K --> N
    L --> N
    M --> N
    
    N -->|Yes| O[Trigger Alert]
    N -->|No| P[Continue Monitoring]
    
    O --> Q[Investigate Issue]
    Q --> R[Apply Fix]
    R --> S[Verify Resolution]
    S --> P
```

## Deployment Pipeline

```mermaid
graph TD
    A[Code Changes] --> B[Run Tests]
    B --> C{Tests Pass?}
    C -->|No| D[Fix Issues]
    D --> B
    
    C -->|Yes| E[Database Migrations]
    E --> F[Deploy Edge Functions]
    F --> G[Update Frontend]
    
    G --> H[Verify Webhook Endpoint]
    H --> I[Test Payment Flow]
    I --> J[Monitor Logs]
    
    J --> K{Issues Detected?}
    K -->|Yes| L[Rollback Changes]
    K -->|No| M[Deployment Complete]
    
    L --> N[Investigate & Fix]
    N --> A
    
    M --> O[Update Documentation]
    O --> P[Notify Team]
```

These diagrams provide visual representations of the complex payment flow, error handling, and system architecture implemented in the Stripe webhook enhancements. They can be rendered using Mermaid-compatible tools or platforms that support Mermaid syntax.
