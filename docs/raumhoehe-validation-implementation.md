# Raumhöhe (Room Height) Validation Implementation

## Overview

This document describes the implementation of validation for the "Raumhöhe" (average room height) field in `GebaeudedetailsPage1.tsx`. The validation ensures users enter realistic room height values between 2.0 and 4.0 meters.

## Implementation Details

### Validation Rules

- **Minimum value**: 2.0 meters (typical minimum for habitable rooms)
- **Maximum value**: 4.0 meters (typical maximum for residential buildings)
- **Input format**: Supports both German (comma) and English (dot) decimal separators
- **Field requirement**: Only required for WG/B certificate type
- **Real-time validation**: Shows errors as the user types or leaves the field

### Technical Implementation

#### 1. Validation Function

```typescript
const validateRaumhoehe = useCallback((raumhoehe: string) => {
  if (!raumhoehe || raumhoehe.trim() === '') {
    return null; // No validation if field is empty
  }

  // Convert German decimal format (comma) to English format (dot)
  const normalizedValue = raumhoehe.replace(',', '.');
  
  // Check if the normalized value is a valid number format
  if (!/^\s*\d+(\.\d+)?\s*$/.test(normalizedValue)) {
    return 'Bitte geben Sie eine gültige Zahl ein';
  }
  
  const height = parseFloat(normalizedValue);

  if (isNaN(height)) {
    return 'Bitte geben Sie eine gültige Zahl ein';
  }

  if (height < 2.0 || height > 4.0) {
    return 'Die Raumhöhe muss zwischen 2,0 und 4,0 Metern liegen';
  }

  return null;
}, []);
```

#### 2. Real-time Validation

The validation is triggered in real-time using a debounced `useEffect` hook:

```typescript
useEffect(() => {
  const raumhoeheValue = raumhoeheField.state.value || '';

  if (raumhoeheValidationTimeoutRef.current) {
    clearTimeout(raumhoeheValidationTimeoutRef.current);
  }

  if (certificateType === 'WG/B') {
    raumhoeheValidationTimeoutRef.current = setTimeout(() => {
      const validationError = validateRaumhoehe(raumhoeheValue);
      setRaumhoeheError(validationError);
    }, 500); // 500ms debounce
  } else {
    setRaumhoeheError(null);
  }

  return () => {
    if (raumhoeheValidationTimeoutRef.current) {
      clearTimeout(raumhoeheValidationTimeoutRef.current);
    }
  };
}, [raumhoeheField.state.value, certificateType, validateRaumhoehe]);
```

#### 3. Form Submission Validation

The validation is also checked during form submission:

```typescript
// Validate room height for WG/B certificate type
let raumhoeheValidationError = null;
if (certificateType === 'WG/B' && value.Raumhöhe) {
  raumhoeheValidationError = validateRaumhoehe(value.Raumhöhe);
}

if (baujahrHzErzValidationError || raumhoeheValidationError) {
  if (raumhoeheValidationError) {
    setRaumhoeheError(raumhoeheValidationError);
  }
  setSubmitError('Bitte korrigieren Sie die markierten Eingaben.');
  return;
}
```

#### 4. Custom Form Component

A custom `RaumhoeheFieldWithValidation` component was created to handle the validation display:

```typescript
const RaumhoeheFieldWithValidation = memo(({
  name,
  label,
  placeholder = '',
  required = true,
  form,
  helpText,
  customError
}) => {
  // Component implementation with error display
});
```

### User Experience Features

1. **German Error Messages**: All error messages are in German for better user experience
2. **Visual Indicators**: Red borders, error icons, and error messages for invalid inputs
3. **Help Text**: Updated to include the valid range information
4. **Debounced Validation**: 500ms debounce prevents excessive validation during typing
5. **German Number Format**: Supports comma as decimal separator (e.g., "2,5")
6. **Conditional Display**: Only shows for WG/B certificate type

### Error Messages

- **Invalid format**: "Bitte geben Sie eine gültige Zahl ein"
- **Out of range**: "Die Raumhöhe muss zwischen 2,0 und 4,0 Metern liegen"

### Test Coverage

Comprehensive test suite covers:
- Valid values with dot and comma decimal separators
- Integer values within range
- Empty/whitespace values (no validation)
- Values below minimum (< 2.0m)
- Values above maximum (> 4.0m)
- Non-numeric inputs
- Edge cases and boundary values

### Integration

The validation integrates seamlessly with the existing form validation system:
- Uses the same patterns as other custom validations (e.g., `baujahrHzErz`)
- Follows the project's validation architecture
- Compatible with the enhanced form field components
- Maintains consistency with other form validations

## Usage

The validation is automatically applied to the Raumhöhe field when:
1. The certificate type is WG/B
2. The user enters or modifies the room height value
3. The form is submitted

No additional configuration is required - the validation is built into the form component.
