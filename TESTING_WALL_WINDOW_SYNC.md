# Testing Guide: Wall-Window Synchronization Feature

## Overview

This guide provides step-by-step instructions for testing the wall-window synchronization feature implemented between GebaeudeformPage.tsx and FensterPage.tsx.

## Prerequisites

- Application running locally
- Access to a certificate with type 'WG/B' (only these certificates have the Fenster page)
- Browser with developer tools (for debugging if needed)

## Test Scenarios

### 1. Basic Synchronization Test

**Objective**: Verify that windows are automatically created for each wall

**Steps**:
1. Navigate to `/erfassen/gebaeudeform`
2. Select a building shape (e.g., "Freistehendes Einfamilienhaus")
3. Observe that walls are automatically generated with names like:
   - "Außenwand Nord"
   - "Außenwand Ost" 
   - "Außenwand Süd"
   - "Außenwand West"
4. Fill in required wall data (at minimum the "Fläche" field)
5. Click "Weiter" to navigate to FensterPage
6. **Expected Result**: 
   - 4 windows automatically created
   - Each window has the same name as its corresponding wall
   - Blue information panel explains synchronization
   - Synchronized windows have blue styling and "Mit Wand synchronisiert" badge

### 2. Wall Addition/Removal Test

**Objective**: Test dynamic synchronization when walls change

**Steps**:
1. On GebaeudeformPage, click "Außenwand hinzufügen"
2. Name the new wall "Zusätzliche Außenwand"
3. Fill in required data and save
4. Navigate to FensterPage
5. **Expected Result**: New window "Zusätzliche Außenwand" appears
6. Go back to GebaeudeformPage
7. Remove the additional wall
8. Return to FensterPage
9. **Expected Result**: The additional window is removed

### 3. Wall Renaming Test

**Objective**: Verify that window names update when wall names change

**Steps**:
1. On GebaeudeformPage, change a wall name from "Außenwand Nord" to "Nordwand Hauptgebäude"
2. Save and navigate to FensterPage
3. **Expected Result**: 
   - Corresponding window name updates to "Nordwand Hauptgebäude"
   - Other window properties (area, type, etc.) remain unchanged if previously set
   - Window maintains its synchronized status (blue styling)

### 4. Manual Window Addition Test

**Objective**: Test that manually added windows coexist with synchronized ones

**Steps**:
1. On FensterPage with existing synchronized windows
2. Click "Zusätzliches Fenster hinzufügen"
3. **Expected Result**: 
   - New window added with name like "Zusätzliches Fenster"
   - Gray styling (not blue) indicating manual window
   - No synchronization badge
   - Name field is editable
4. Navigate back to GebaeudeformPage and then return
5. **Expected Result**: Manual window persists alongside synchronized ones

### 5. Mixed Synchronization Test

**Objective**: Test complex scenario with both synchronized and manual windows

**Steps**:
1. Start with 3 walls on GebaeudeformPage
2. Navigate to FensterPage (should have 3 synchronized windows)
3. Add 2 manual windows
4. Customize properties of synchronized windows (area, type, orientation)
5. Go back to GebaeudeformPage
6. Add 1 wall, remove 1 wall, rename 1 wall
7. Return to FensterPage
8. **Expected Result**:
   - Total windows match new wall count + manual windows
   - Renamed wall's window updates name but keeps custom properties
   - Manual windows remain unchanged
   - Status summary shows correct counts

### 6. Edge Case Tests

#### 6.1 No Walls Scenario
**Steps**:
1. Create a new certificate
2. Go directly to FensterPage without defining walls
3. **Expected Result**: Shows default window, no synchronization info

#### 6.2 Empty Walls After Sync
**Steps**:
1. Set up synchronized windows
2. Go back and remove ALL walls
3. Return to FensterPage
4. **Expected Result**: Only manual windows remain, synchronized ones removed

#### 6.3 Large Number of Walls
**Steps**:
1. Add 10+ walls with varied names
2. Navigate to FensterPage
3. **Expected Result**: All windows created, performance remains good

### 7. User Experience Tests

#### 7.1 Loading States
**Steps**:
1. Navigate from GebaeudeformPage to FensterPage
2. **Expected Result**: Brief loading indicator during synchronization

#### 7.2 Information Panels
**Steps**:
1. First visit to FensterPage with walls
2. **Expected Result**: 
   - Blue info panel explaining synchronization
   - Can dismiss panel with "Verstanden, ausblenden"
   - Success message showing sync results

#### 7.3 Visual Indicators
**Steps**:
1. Verify synchronized windows have:
   - Blue background and border
   - "Mit Wand synchronisiert" badge
   - Read-only name field with tooltip
   - Proper form validation

## Validation Checklist

After testing, verify:

- [ ] **Data Consistency**: Window names match wall names exactly
- [ ] **Persistence**: Changes survive page refreshes
- [ ] **Performance**: No noticeable lag during synchronization
- [ ] **Accessibility**: All interactive elements are keyboard accessible
- [ ] **Error Handling**: Graceful degradation if queries fail
- [ ] **Visual Design**: Consistent styling and clear differentiation
- [ ] **Responsive**: Works on different screen sizes

## Common Issues and Solutions

### Issue: Windows not synchronizing
**Check**:
- Certificate type is 'WG/B'
- Wall data exists in gebaeudedetails2.waende
- Browser console for React query errors

### Issue: Duplicate windows appearing
**Check**:
- useEffect dependencies in FensterPage
- Query invalidation timing
- React StrictMode double-rendering

### Issue: Synchronization info not showing
**Check**:
- wallsData is properly loaded as array
- showSyncInfo state management
- Conditional rendering logic

## Performance Testing

### Load Testing
1. Create certificate with 20+ walls
2. Navigate between pages multiple times
3. Monitor React DevTools for unnecessary re-renders

### Memory Testing
1. Perform full test cycle multiple times
2. Check for memory leaks in browser dev tools
3. Verify query cache is properly managed

## Browser Compatibility

Test in:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

## Debugging Tips

### React Query DevTools
- Enable React Query DevTools to monitor cache invalidation
- Check query keys and data structure
- Verify mutation success/error states

### Browser DevTools
- Console: Check for TypeScript errors or warnings
- Network: Verify Supabase requests
- React DevTools: Monitor component re-renders and state

### Logging
The implementation includes console.error for validation issues. Check browser console for:
- Form validation errors
- Query failure messages
- Synchronization status logs

## Regression Testing

After any changes to either page, re-run:
1. Basic synchronization test (#1)
2. Mixed synchronization test (#5)
3. All edge cases (#6)

This ensures the bidirectional relationship remains intact.

## Test Data Setup

For consistent testing, use walls with names:
- "Außenwand Nord" (standard)
- "Außenwand Süd" (standard)  
- "Giebel Ost" (custom)
- "Traufseite West" (custom)
- "Zusätzliche Außenwand" (for addition tests)

This provides good coverage of naming patterns and synchronization scenarios.