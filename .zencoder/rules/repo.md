---
description: Repository Information Overview
alwaysApply: true
---

# Verbrauchsausweis-App Information

## Summary
A web application for creating energy performance certificates ("Energieausweis") for buildings. Users can input building data through forms, upload images and consumption bills, view a summary of their data, and purchase certificates via Stripe. Administrators can manage certificate data and export it as CSV.

## Structure
- **src/**: Main React application code (components, contexts, hooks, pages)
- **supabase/**: Serverless functions for backend operations (payment processing, webhooks)
- **public/**: Static assets
- **docs/**: Project documentation
- **migrations/**: Database migration scripts

## Language & Runtime
**Language**: TypeScript
**Version**: ~5.8.3
**Build System**: Vite (v6.3.5)
**Package Manager**: npm

## Dependencies
**Main Dependencies**:
- React (v19.1.0)
- @supabase/supabase-js (v2.49.4)
- @tanstack/react-query (v5.75.5)
- @tanstack/react-router (v1.120.2)
- @tanstack/react-form (v1.10.0)
- Stripe (v14, used in Supabase functions)
- Resend (v2.1.0, for email notifications)

**Development Dependencies**:
- TypeScript (v5.8.3)
- Vite (v6.3.5)
- Tailwind CSS (v3.4.17)
- ESLint (v9.25.0)
- Vitest (for testing)

## Build & Installation
```bash
# Install dependencies
npm install

# Development server
npm run dev

# Build for production
npm run build

# Run tests
npm run test
```

## Docker
No Docker configuration found in the repository.

## Testing
**Framework**: Vitest with Testing Library
**Test Location**: src/test/ and component-specific __tests__ directories
**Configuration**: Configured in vite.config.ts
**Run Command**:
```bash
npm run test       # Run tests in watch mode
npm run test:ui    # Run tests with UI
npm run test:run   # Run tests once
```

## Supabase Functions
**Runtime**: Deno
**Functions**:
- stripe-webhook: Handles Stripe payment webhooks
- create-checkout-session: Creates Stripe checkout sessions
- email-webhook: Manages email notifications
- check-abandoned-sessions: Monitors incomplete sessions
- transfer-certificate-files: Manages certificate file transfers

## Database
**Provider**: Supabase (PostgreSQL)
**Main Tables**:
- energieausweise: Stores certificate data
- email_logs: Tracks email notifications
- payment_attempts: Records payment processing attempts