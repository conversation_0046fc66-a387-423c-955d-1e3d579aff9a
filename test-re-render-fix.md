# Window Focus Re-render Fix - Comprehensive Solution

## Root Cause Analysis - Updated

The excessive re-renders when switching browser windows/tabs were caused by:

### **1. Missing Global QueryClient Configuration (PRIMARY ISSUE)**
- `QueryClient` created without default options in `App.tsx`
- React Query's default `refetchOnWindowFocus: true` affected ALL queries globally
- Individual component optimizations were inconsistent and insufficient

### **2. Cascading Re-render Chain**
1. Window focus → Global refetchOnWindowFocus triggers
2. Multiple queries refetch simultaneously across the app
3. AuthContext re-renders due to Supabase auth state changes
4. CertificateContext re-renders due to auth changes
5. Router context updates in App.tsx
6. ZusammenfassungPage re-renders multiple times
7. useCertificateFiles hook called repeatedly

### **3. Previous Issues (Now Resolved)**
- Flawed useMemo implementation in useCertificateFiles hook
- Multiple hook instances in VerbrauchPage
- Missing prop memoization

## Comprehensive Fixes Applied

### **1. Global QueryClient Configuration (PRIMARY FIX)**
```typescript
// src/App.tsx
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,    // KEY: Prevents window focus refetches globally
      refetchOnMount: false,          // Only refetch if data is stale
      refetchOnReconnect: false,      // Don't refetch on network reconnect
      staleTime: 5 * 60 * 1000,      // 5 minutes default cache
      gcTime: 10 * 60 * 1000,        // 10 minutes garbage collection
      retry: 1,                       // Reduce retry attempts
      retryDelay: 1000,              // Shorter retry delay
    },
    mutations: {
      retry: 1,
      retryDelay: 1000,
    },
  },
});
```

### **2. Simplified Component Configurations**
- Removed redundant query options from individual components
- Only override global defaults where specifically needed
- Consistent behavior across all queries

### **3. Added React Query DevTools**
- Installed `@tanstack/react-query-devtools`
- Added to App.tsx for better debugging and monitoring

### **4. Previous Fixes (Maintained)**
- Fixed useCertificateFiles hook change detection logic
- Maintained prop memoization optimizations
- Kept AuthContext debouncing

## Expected Results

### **Immediate Improvements**
- **No more excessive re-renders** when switching browser windows/tabs
- **Consistent query behavior** across the entire application
- **Significantly reduced console logging** from hooks and components
- **Better performance** due to eliminated unnecessary refetches

### **Long-term Benefits**
- **Predictable caching behavior** for all new queries
- **Easier debugging** with React Query DevTools
- **Consistent performance** across different pages and components

## Comprehensive Test Plan

### **Test 1: Window Focus Behavior**
1. Navigate to `/erfassen/zusammenfassung`
2. Switch to another browser tab/window
3. Return to the application tab
4. **Expected**: Minimal or no console logging, no visible re-renders

### **Test 2: Cross-Page Navigation**
1. Navigate between different pages (VerbrauchPage ↔ ZusammenfassungPage)
2. **Expected**: Normal navigation behavior, no excessive hook calls

### **Test 3: React Query DevTools Verification**
1. Open React Query DevTools (floating button in bottom-right)
2. Monitor query states during window focus changes
3. **Expected**: Queries remain in "fresh" state, no automatic refetching

### **Test 4: Performance Monitoring**
1. Open browser DevTools → Performance tab
2. Record performance while switching tabs
3. **Expected**: Reduced JavaScript execution time, fewer re-renders
