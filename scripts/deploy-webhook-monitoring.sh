#!/bin/bash

# Deploy Webhook Monitoring System
# This script deploys all components needed for webhook health monitoring

set -e

echo "🚀 Deploying Webhook Monitoring System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "supabase" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    print_error "Supabase CLI is not installed. Please install it first:"
    echo "npm install -g supabase"
    exit 1
fi

# Check if we're logged in to Supabase
if ! supabase projects list &> /dev/null; then
    print_error "Please log in to Supabase first:"
    echo "supabase login"
    exit 1
fi

print_status "Starting deployment process..."

# Step 1: Run database migrations
print_status "1. Running database migrations..."
if supabase db push; then
    print_success "Database migrations completed"
else
    print_error "Database migrations failed"
    exit 1
fi

# Step 2: Deploy Edge Functions
print_status "2. Deploying Edge Functions..."

# Deploy webhook monitor function
print_status "   Deploying webhook-monitor function..."
if supabase functions deploy webhook-monitor --no-verify-jwt; then
    print_success "   webhook-monitor function deployed"
else
    print_error "   Failed to deploy webhook-monitor function"
    exit 1
fi

# Deploy scheduled webhook monitor function
print_status "   Deploying scheduled-webhook-monitor function..."
if supabase functions deploy scheduled-webhook-monitor --no-verify-jwt; then
    print_success "   scheduled-webhook-monitor function deployed"
else
    print_error "   Failed to deploy scheduled-webhook-monitor function"
    exit 1
fi

# Step 3: Set up environment variables
print_status "3. Checking environment variables..."

# Check required environment variables
REQUIRED_VARS=("RESEND_API_KEY" "ADMIN_EMAIL" "FROM_EMAIL")
MISSING_VARS=()

for var in "${REQUIRED_VARS[@]}"; do
    if ! supabase secrets list | grep -q "$var"; then
        MISSING_VARS+=("$var")
    fi
done

if [ ${#MISSING_VARS[@]} -gt 0 ]; then
    print_warning "Missing environment variables:"
    for var in "${MISSING_VARS[@]}"; do
        echo "  - $var"
    done
    echo ""
    print_status "Please set these variables using:"
    for var in "${MISSING_VARS[@]}"; do
        echo "  supabase secrets set $var=your_value_here"
    done
    echo ""
    read -p "Have you set all required environment variables? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_error "Please set the required environment variables and run this script again"
        exit 1
    fi
else
    print_success "All required environment variables are set"
fi

# Step 4: Test the webhook monitor function
print_status "4. Testing webhook monitor function..."
PROJECT_REF=$(supabase status | grep "API URL" | awk '{print $3}' | sed 's|https://||' | sed 's|\.supabase\.co||')

if [ -z "$PROJECT_REF" ]; then
    print_warning "Could not determine project reference. Skipping function test."
else
    print_status "   Testing function accessibility..."
    
    # Get the function URL
    FUNCTION_URL="https://${PROJECT_REF}.supabase.co/functions/v1/webhook-monitor"
    
    # Test the function
    if curl -s -f -X POST "$FUNCTION_URL" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $(supabase secrets list | grep SUPABASE_SERVICE_ROLE_KEY | awk '{print $2}')" \
        -d '{"test": true}' > /dev/null; then
        print_success "   Webhook monitor function is accessible"
    else
        print_warning "   Could not test webhook monitor function (this may be normal)"
    fi
fi

# Step 5: Set up cron job (optional)
print_status "5. Setting up scheduled monitoring..."
print_status "   You can set up a cron job to run health checks automatically:"
echo ""
echo "   Example cron job (runs every 15 minutes):"
echo "   */15 * * * * curl -X POST https://${PROJECT_REF}.supabase.co/functions/v1/scheduled-webhook-monitor"
echo ""

# Step 6: Verify deployment
print_status "6. Verifying deployment..."

# Check if tables exist
print_status "   Checking database tables..."
if supabase db diff --schema public | grep -q "webhook_health_checks"; then
    print_success "   webhook_health_checks table exists"
else
    print_warning "   webhook_health_checks table may not exist"
fi

# Step 7: Display next steps
print_success "🎉 Webhook Monitoring System deployment completed!"
echo ""
print_status "Next steps:"
echo "1. 🔍 Check your admin dashboard to see the webhook health monitor"
echo "2. 📧 Verify that email alerts are working by triggering a test"
echo "3. ⏰ Set up a cron job for automated monitoring (optional)"
echo "4. 🔧 Ensure 'Verify JWT with legacy secret' is DISABLED in Supabase settings"
echo ""
print_status "Important URLs:"
echo "• Admin Dashboard: https://your-domain.com/admin"
echo "• Webhook Monitor: https://${PROJECT_REF}.supabase.co/functions/v1/webhook-monitor"
echo "• Supabase Dashboard: https://supabase.com/dashboard/project/${PROJECT_REF}"
echo ""
print_warning "Remember to:"
echo "• Keep 'Verify JWT with legacy secret' DISABLED"
echo "• Monitor the webhook health status regularly"
echo "• Test the alert system periodically"
echo ""
print_success "Deployment complete! 🚀"
