import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.49.4";
import Stripe from "https://esm.sh/stripe@14?target=denonext";
// CORS headers to allow cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};
serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }
  try {
    const stripeSecretKey = Deno.env.get("STRIPE_SECRET_KEY");
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
    if (!stripeSecretKey) {
      throw new Error("STRIPE_SECRET_KEY environment variable is not set");
    }
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("Supabase environment variables are not set");
    }
    const stripe = new Stripe(stripeSecretKey);
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    // Parse the request body
    const { certificateType, successUrl, cancelUrl, paymentId, userEmail, orderNumber } = await req.json();
    if (!certificateType || !successUrl || !cancelUrl || !paymentId) {
      throw new Error("Missing required parameters: certificateType, successUrl, cancelUrl, paymentId");
    }
    // Fetch pricing information from database
    const { data: pricingData, error: pricingError } = await supabase.from('certificate_pricing').select('price_cents, stripe_price_id, display_name, description').eq('certificate_type', certificateType).eq('is_active', true).single();
    if (pricingError || !pricingData) {
      console.error("Error fetching pricing data:", pricingError);
      throw new Error(`Pricing not found for certificate type: ${certificateType}`);
    }
    const { price_cents: amount, stripe_price_id, display_name, description } = pricingData;
    console.log("Creating checkout session with params:", {
      certificateType,
      amount,
      stripe_price_id,
      successUrl,
      cancelUrl,
      paymentId,
      userEmail,
      orderNumber
    });
    // Create a Stripe checkout session
    const sessionConfig = {
      mode: 'payment',
      success_url: successUrl,
      cancel_url: cancelUrl,
      client_reference_id: paymentId,
      customer_email: userEmail,
      metadata: {
        order_number: orderNumber,
        certificate_id: paymentId,
        certificate_type: certificateType
      },
      // Additional settings for better UX
      billing_address_collection: 'auto',
      payment_intent_data: {
        metadata: {
          order_number: orderNumber,
          certificate_id: paymentId,
          certificate_type: certificateType
        }
      },
      // Customize the checkout experience
      locale: 'de',
      allow_promotion_codes: false
    };
    // Use Stripe Price ID if available, otherwise create price_data
    if (stripe_price_id) {
      sessionConfig.line_items = [
        {
          price: stripe_price_id,
          quantity: 1
        }
      ];
    } else {
      sessionConfig.line_items = [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: display_name || 'Energieausweis',
              description: description || 'Ihr personalisierter Energieausweis nach aktuellen Standards',
              images: []
            },
            unit_amount: amount
          },
          quantity: 1
        }
      ];
    }
    const session = await stripe.checkout.sessions.create(sessionConfig);
    console.log("Checkout session created:", session.id);
    return new Response(JSON.stringify({
      id: session.id,
      url: session.url,
      payment_status: session.payment_status
    }), {
      status: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error(`Error creating checkout session: ${error.message}`);
    // Determine error code for better client-side handling
    let errorCode = 'session_creation_failed';
    let status = 500;
    if (error.message.includes('authentication')) {
      errorCode = 'authentication_error';
      status = 401;
    } else if (error.message.includes('parameter')) {
      errorCode = 'invalid_parameters';
      status = 400;
    } else if (error.message.includes('Stripe')) {
      errorCode = 'stripe_error';
    }
    return new Response(JSON.stringify({
      error: error.message,
      code: errorCode
    }), {
      status: status,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
