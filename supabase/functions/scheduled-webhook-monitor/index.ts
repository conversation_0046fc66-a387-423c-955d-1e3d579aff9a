// Scheduled Webhook Health Monitor
// This function runs automatically via cron to monitor webhook health

import { createClient } from "https://esm.sh/@supabase/supabase-js@2.49.1";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Main scheduled function
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    console.log('Starting scheduled webhook health check...');

    // Call the webhook monitor function
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    if (!supabaseUrl) {
      throw new Error("Missing SUPABASE_URL");
    }

    const monitorResponse = await fetch(`${supabaseUrl}/functions/v1/webhook-monitor`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")}`
      },
      body: JSON.stringify({ scheduled: true })
    });

    const monitorResult = await monitorResponse.json();

    if (!monitorResponse.ok) {
      throw new Error(`Monitor function failed: ${monitorResult.error}`);
    }

    console.log('Scheduled webhook health check completed successfully');

    return new Response(JSON.stringify({
      success: true,
      message: 'Scheduled webhook health check completed',
      monitor_result: monitorResult,
      timestamp: new Date().toISOString()
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in scheduled webhook monitor:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});
