import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

interface TransferRequest {
  sourceUserId: string;
  targetUserId: string;
  certificateId: string;
}

interface TransferResponse {
  success: boolean;
  error?: string;
  transferredFiles?: number;
  failedFiles?: string[];
  totalFiles?: number;
}

// CORS headers to allow cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

Deno.serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }

  try {
    // Parse request body
    const { sourceUserId, targetUserId, certificateId }: TransferRequest = await req.json();

    // Validate inputs
    if (!sourceUserId || !targetUserId || !certificateId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing required parameters: sourceUserId, targetUserId, certificateId'
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Check if source and target are the same
    if (sourceUserId === targetUserId) {
      return new Response(
        JSON.stringify({
          success: true,
          message: 'Source and target users are the same, no transfer needed',
          transferredFiles: 0
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Create Supabase client with service role key (bypasses RLS)
    const supabaseServiceRole = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    console.log(`🔄 Starting file transfer: ${sourceUserId}/${certificateId} -> ${targetUserId}/${certificateId}`);

    // First, check if the source directory exists by trying to list it
    console.log(`📂 Checking source directory: ${sourceUserId}/${certificateId}`);

    // List all files in source directory
    const { data: sourceFiles, error: listError } = await supabaseServiceRole.storage
      .from('certificateuploads')
      .list(`${sourceUserId}/${certificateId}`);

    if (listError) {
      console.error('❌ Error listing source files:', listError);
      console.error('❌ Full error details:', JSON.stringify(listError, null, 2));

      // Check if it's a "not found" error
      if (listError.message?.includes('not found') || listError.message?.includes('Object not found')) {
        return new Response(
          JSON.stringify({
            success: false,
            error: `Source directory not found: ${sourceUserId}/${certificateId}. No files to transfer.`
          }),
          { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      return new Response(
        JSON.stringify({
          success: false,
          error: `Failed to list source files: ${listError.message}`
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    if (!sourceFiles || sourceFiles.length === 0) {
      console.log('ℹ️ No files found in source directory');
      return new Response(
        JSON.stringify({
          success: true,
          message: 'No files to transfer',
          transferredFiles: 0,
          totalFiles: 0
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log(`📊 Found ${sourceFiles.length} files to transfer:`, sourceFiles.map(f => f.name));

    let transferredCount = 0;
    const failedFiles: string[] = [];

    // Transfer each file
    for (const file of sourceFiles) {
      try {
        const sourceFilePath = `${sourceUserId}/${certificateId}/${file.name}`;
        const targetFilePath = `${targetUserId}/${certificateId}/${file.name}`;

        console.log(`📄 Transferring file ${transferredCount + 1}/${sourceFiles.length}: ${sourceFilePath} -> ${targetFilePath}`);

        // Download the file from source
        console.log(`⬇️ Downloading from: ${sourceFilePath}`);
        const { data: fileData, error: downloadError } = await supabaseServiceRole.storage
          .from('certificateuploads')
          .download(sourceFilePath);

        if (downloadError || !fileData) {
          console.error(`❌ Failed to download file ${file.name}:`, downloadError);
          console.error(`❌ Download error details:`, JSON.stringify(downloadError, null, 2));
          failedFiles.push(file.name);
          continue;
        }

        console.log(`✅ Downloaded file ${file.name}, size: ${fileData.size} bytes`);

        // Upload the file to target location
        console.log(`⬆️ Uploading to: ${targetFilePath}`);
        const { error: uploadError } = await supabaseServiceRole.storage
          .from('certificateuploads')
          .upload(targetFilePath, fileData, {
            upsert: false // Don't overwrite existing files
          });

        if (uploadError) {
          console.error(`❌ Failed to upload file ${file.name}:`, uploadError);
          console.error(`❌ Upload error details:`, JSON.stringify(uploadError, null, 2));
          failedFiles.push(file.name);
          continue;
        }

        console.log(`✅ Uploaded file ${file.name}`);

        // Verify the copy was successful
        console.log(`🔍 Verifying upload of ${file.name}`);
        const { data: verifyFiles, error: verifyError } = await supabaseServiceRole.storage
          .from('certificateuploads')
          .list(`${targetUserId}/${certificateId}`, {
            search: file.name
          });

        if (verifyError) {
          console.error(`❌ Verification error for ${file.name}:`, verifyError);
          failedFiles.push(file.name);
          continue;
        }

        if (!verifyFiles || verifyFiles.length === 0) {
          console.error(`❌ Copy verification failed for ${file.name} - file not found in target directory`);
          failedFiles.push(file.name);
          continue;
        }

        console.log(`✅ Verified upload of ${file.name}`);

        // Delete the original file
        console.log(`🗑️ Deleting original file: ${sourceFilePath}`);
        const { error: deleteError } = await supabaseServiceRole.storage
          .from('certificateuploads')
          .remove([sourceFilePath]);

        if (deleteError) {
          console.warn(`⚠️ Failed to delete original file ${sourceFilePath}, but copy succeeded:`, deleteError);
          console.warn(`⚠️ Delete error details:`, JSON.stringify(deleteError, null, 2));
          // Don't count this as a failure since the copy succeeded
        } else {
          console.log(`✅ Deleted original file: ${sourceFilePath}`);
        }

        transferredCount++;
        console.log(`✅ Successfully transferred: ${file.name} (${transferredCount}/${sourceFiles.length})`);

      } catch (error) {
        console.error(`❌ Error transferring file ${file.name}:`, error);
        console.error(`❌ Transfer error details:`, JSON.stringify(error, null, 2));
        failedFiles.push(file.name);
      }
    }

    const success = failedFiles.length === 0;
    const response: TransferResponse = {
      success,
      transferredFiles: transferredCount,
      totalFiles: sourceFiles.length,
      ...(failedFiles.length > 0 && { failedFiles })
    };

    if (success) {
      console.log(`✅ File transfer completed successfully: ${transferredCount} files transferred`);
    } else {
      console.error(`❌ File transfer completed with errors: ${transferredCount}/${sourceFiles.length} files transferred`);
      response.error = `Failed to transfer ${failedFiles.length} files: ${failedFiles.join(', ')}`;
    }

    return new Response(
      JSON.stringify(response),
      {
        status: success ? 200 : 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error: any) {
    console.error('❌ Unexpected error during file transfer:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Unexpected error during file transfer'
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
