// Webhook Monitoring and Alerting System
// This Edge Function monitors webhook health and sends alerts for failures

import { createClient } from "https://esm.sh/@supabase/supabase-js@2.49.1";
import { Resend } from "https://esm.sh/resend@2.1.0";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface WebhookHealthCheck {
  id?: string;
  check_timestamp: string;
  jwt_verification_enabled: boolean;
  webhook_function_accessible: boolean;
  last_successful_webhook?: string;
  failed_webhooks_count_24h: number;
  alert_sent: boolean;
  error_details?: string;
}

interface AlertConfig {
  admin_email: string;
  webhook_failure_threshold: number;
  jwt_verification_alert: boolean;
  resend_api_key: string;
}

class WebhookMonitor {
  private supabase: any;
  private resend: Resend;
  private config: AlertConfig;

  constructor() {
    const supabaseUrl = Deno.env.get("SUPABASE_URL");
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
    const resendApiKey = Deno.env.get("RESEND_API_KEY");
    const adminEmail = Deno.env.get("ADMIN_EMAIL") || "<EMAIL>";

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error("Missing Supabase credentials");
    }

    if (!resendApiKey) {
      throw new Error("Missing Resend API key");
    }

    this.supabase = createClient(supabaseUrl, supabaseServiceKey);
    this.resend = new Resend(resendApiKey);
    
    this.config = {
      admin_email: adminEmail,
      webhook_failure_threshold: 3, // Alert after 3 failed webhooks in 24h
      jwt_verification_alert: true,
      resend_api_key: resendApiKey
    };
  }

  // Check if JWT verification is enabled (this would break webhooks)
  async checkJWTVerificationStatus(): Promise<boolean> {
    try {
      // This is a simplified check - in practice, you'd need to check the actual Supabase settings
      // For now, we'll check if webhook function is accessible without JWT
      const response = await fetch(`${Deno.env.get("SUPABASE_URL")}/functions/v1/stripe-webhook`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ test: true })
      });

      // If we get 401, JWT verification is likely enabled
      return response.status === 401;
    } catch (error) {
      console.error('Error checking JWT verification status:', error);
      return false;
    }
  }

  // Check webhook function accessibility
  async checkWebhookAccessibility(): Promise<{ accessible: boolean; error?: string }> {
    try {
      const response = await fetch(`${Deno.env.get("SUPABASE_URL")}/functions/v1/stripe-webhook`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Stripe-Signature': 'test_signature'
        },
        body: JSON.stringify({ test: true })
      });

      // We expect a 400 (bad signature) or 200, not 401 (unauthorized)
      const accessible = response.status !== 401;
      const error = response.status === 401 ? 'JWT verification may be enabled' : undefined;

      return { accessible, error };
    } catch (error) {
      return { accessible: false, error: error.message };
    }
  }

  // Get failed webhook count in last 24 hours
  async getFailedWebhookCount(): Promise<number> {
    try {
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

      const { data, error } = await this.supabase
        .from('stripe_webhook_events')
        .select('id')
        .eq('processing_status', 'error')
        .gte('created_at', twentyFourHoursAgo);

      if (error) {
        console.error('Error fetching failed webhook count:', error);
        return 0;
      }

      return data?.length || 0;
    } catch (error) {
      console.error('Error getting failed webhook count:', error);
      return 0;
    }
  }

  // Get last successful webhook timestamp
  async getLastSuccessfulWebhook(): Promise<string | null> {
    try {
      const { data, error } = await this.supabase
        .from('stripe_webhook_events')
        .select('created_at')
        .eq('processing_status', 'handled')
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error || !data) {
        return null;
      }

      return data.created_at;
    } catch (error) {
      console.error('Error getting last successful webhook:', error);
      return null;
    }
  }

  // Send alert email
  async sendAlert(healthCheck: WebhookHealthCheck): Promise<void> {
    try {
      let subject = "🚨 Webhook Alert - K2 Energieausweis";
      let alertType = "";
      let priority = "high";

      if (healthCheck.jwt_verification_enabled) {
        subject = "🔴 CRITICAL: JWT Verification Breaking Webhooks";
        alertType = "JWT_VERIFICATION_ENABLED";
        priority = "critical";
      } else if (healthCheck.failed_webhooks_count_24h >= this.config.webhook_failure_threshold) {
        subject = `⚠️ Multiple Webhook Failures (${healthCheck.failed_webhooks_count_24h})`;
        alertType = "MULTIPLE_FAILURES";
      } else if (!healthCheck.webhook_function_accessible) {
        subject = "🔴 Webhook Function Inaccessible";
        alertType = "FUNCTION_INACCESSIBLE";
        priority = "critical";
      }

      const emailContent = this.generateAlertEmail(healthCheck, alertType);

      const { data, error } = await this.resend.emails.send({
        from: '<EMAIL>',
        to: [this.config.admin_email],
        subject: subject,
        html: emailContent,
        tags: [
          { name: 'type', value: 'webhook_alert' },
          { name: 'priority', value: priority },
          { name: 'alert_type', value: alertType }
        ]
      });

      if (error) {
        console.error('Error sending alert email:', error);
      } else {
        console.log('Alert email sent successfully:', data?.id);
      }
    } catch (error) {
      console.error('Error in sendAlert:', error);
    }
  }

  // Generate alert email content
  private generateAlertEmail(healthCheck: WebhookHealthCheck, alertType: string): string {
    const timestamp = new Date(healthCheck.check_timestamp).toLocaleString('de-DE');
    
    let content = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #dc2626;">Webhook Monitoring Alert</h2>
      <p><strong>Zeitpunkt:</strong> ${timestamp}</p>
    `;

    if (alertType === "JWT_VERIFICATION_ENABLED") {
      content += `
        <div style="background-color: #fef2f2; border: 1px solid #fecaca; padding: 16px; border-radius: 8px; margin: 16px 0;">
          <h3 style="color: #dc2626; margin-top: 0;">🔴 KRITISCH: JWT-Verifizierung aktiviert</h3>
          <p>Die Einstellung "Verify JWT with legacy secret" ist in Supabase aktiviert. Dies verhindert, dass Stripe-Webhooks funktionieren.</p>
          <p><strong>Sofortige Maßnahme erforderlich:</strong></p>
          <ol>
            <li>Gehen Sie zu Ihrem Supabase Dashboard</li>
            <li>Navigieren Sie zu Settings → API</li>
            <li>Deaktivieren Sie "Verify JWT with legacy secret"</li>
          </ol>
          <p style="color: #dc2626;"><strong>Warnung:</strong> Solange diese Einstellung aktiv ist, werden keine Zahlungen verarbeitet!</p>
        </div>
      `;
    }

    if (healthCheck.failed_webhooks_count_24h > 0) {
      content += `
        <div style="background-color: #fef3c7; border: 1px solid #fcd34d; padding: 16px; border-radius: 8px; margin: 16px 0;">
          <h3 style="color: #d97706; margin-top: 0;">⚠️ Fehlgeschlagene Webhooks</h3>
          <p><strong>Anzahl fehlgeschlagener Webhooks (24h):</strong> ${healthCheck.failed_webhooks_count_24h}</p>
          ${healthCheck.error_details ? `<p><strong>Fehlerdetails:</strong> ${healthCheck.error_details}</p>` : ''}
        </div>
      `;
    }

    content += `
      <div style="background-color: #f3f4f6; padding: 16px; border-radius: 8px; margin: 16px 0;">
        <h3 style="margin-top: 0;">System Status</h3>
        <ul>
          <li><strong>JWT-Verifizierung aktiviert:</strong> ${healthCheck.jwt_verification_enabled ? '🔴 Ja' : '✅ Nein'}</li>
          <li><strong>Webhook-Funktion erreichbar:</strong> ${healthCheck.webhook_function_accessible ? '✅ Ja' : '🔴 Nein'}</li>
          <li><strong>Letzter erfolgreicher Webhook:</strong> ${healthCheck.last_successful_webhook ? new Date(healthCheck.last_successful_webhook).toLocaleString('de-DE') : 'Unbekannt'}</li>
        </ul>
      </div>

      <div style="margin-top: 24px; padding-top: 16px; border-top: 1px solid #e5e7eb;">
        <p style="font-size: 14px; color: #6b7280;">
          Diese Nachricht wurde automatisch vom Webhook-Monitoring-System generiert.
          <br>K2 Energieausweis - Webhook Monitor
        </p>
      </div>
    </div>
    `;

    return content;
  }

  // Perform health check
  async performHealthCheck(): Promise<WebhookHealthCheck> {
    const timestamp = new Date().toISOString();
    
    console.log('Starting webhook health check...');

    const jwtVerificationEnabled = await this.checkJWTVerificationStatus();
    const { accessible: webhookAccessible, error } = await this.checkWebhookAccessibility();
    const failedCount = await this.getFailedWebhookCount();
    const lastSuccessful = await this.getLastSuccessfulWebhook();

    const healthCheck: WebhookHealthCheck = {
      check_timestamp: timestamp,
      jwt_verification_enabled: jwtVerificationEnabled,
      webhook_function_accessible: webhookAccessible,
      last_successful_webhook: lastSuccessful,
      failed_webhooks_count_24h: failedCount,
      alert_sent: false,
      error_details: error
    };

    // Determine if alert should be sent
    const shouldAlert = jwtVerificationEnabled || 
                       !webhookAccessible || 
                       failedCount >= this.config.webhook_failure_threshold;

    if (shouldAlert) {
      await this.sendAlert(healthCheck);
      healthCheck.alert_sent = true;
    }

    // Log health check to database
    await this.logHealthCheck(healthCheck);

    return healthCheck;
  }

  // Log health check to database
  private async logHealthCheck(healthCheck: WebhookHealthCheck): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('webhook_health_checks')
        .insert(healthCheck);

      if (error) {
        console.error('Error logging health check:', error);
      }
    } catch (error) {
      console.error('Error in logHealthCheck:', error);
    }
  }
}

// Main handler
Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const monitor = new WebhookMonitor();
    const healthCheck = await monitor.performHealthCheck();

    return new Response(JSON.stringify({
      success: true,
      health_check: healthCheck,
      timestamp: new Date().toISOString()
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in webhook monitor:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});
