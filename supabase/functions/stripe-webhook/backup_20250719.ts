// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.
// Import via bare specifier thanks to the import_map.json file.
import Stripe from 'https://esm.sh/stripe@14?target=denonext';
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.49.1";
import { Resend } from "https://esm.sh/resend@2.1.0";
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY'), {
  // This is needed to use the Fetch API rather than relying on the Node http
  // package.
  apiVersion: '2024-11-20'
});
const webhookSecret = Deno.env.get("STRIPE_WEBHOOK_SECRET");
const cryptoProvider = Stripe.createSubtleCryptoProvider();
// Create Supabase client with service role key for admin access
const supabaseUrl = Deno.env.get("SUPABASE_URL");
const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error("Missing Supabase credentials");
}
const supabase = createClient(supabaseUrl, supabaseServiceKey);
// Initialize Resend for email notifications
const resendApiKey = Deno.env.get("RESEND_API_KEY");
let resend = null;
if (resendApiKey) {
  resend = new Resend(resendApiKey);
  console.log('Resend email service initialized');
} else {
  console.warn('RESEND_API_KEY not found - email notifications will be disabled');
}
// Email templates and configuration
const EMAIL_CONFIG = {
  from: 'Energieausweis Service <<EMAIL>>',
  replyTo: '<EMAIL>'
};
// Email template for successful payment
const createSuccessEmailTemplate = (orderNumber, certificateType)=>{
  const certificateTypeNames = {
    'WG/V': 'Wohngebäude Verbrauchsausweis',
    'WG/B': 'Wohngebäude Bedarfsausweis',
    'NWG/V': 'Nichtwohngebäude Verbrauchsausweis'
  };
  const typeName = certificateTypeNames[certificateType] || certificateType;
  return {
    subject: `Zahlungsbestätigung - Energieausweis ${orderNumber}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0; font-size: 28px;">Zahlungsbestätigung</h1>
            <div style="width: 60px; height: 4px; background-color: #10b981; margin: 15px auto;"></div>
          </div>
          
          <div style="background-color: #f0f9ff; padding: 20px; border-radius: 6px; margin-bottom: 25px; border-left: 4px solid #2563eb;">
            <h2 style="color: #1e40af; margin: 0 0 10px 0; font-size: 20px;">✅ Zahlung erfolgreich erhalten</h2>
            <p style="margin: 0; color: #374151; font-size: 16px;">Vielen Dank für Ihre Zahlung. Ihr Energieausweis wird nun bearbeitet.</p>
          </div>
          
          <div style="margin-bottom: 25px;">
            <h3 style="color: #374151; margin: 0 0 15px 0; font-size: 18px;">Bestelldetails:</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Bestellnummer:</td>
                <td style="padding: 8px 0; color: #111827; font-weight: 600;">${orderNumber}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Zertifikatstyp:</td>
                <td style="padding: 8px 0; color: #111827;">${typeName}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Betrag:</td>
                <td style="padding: 8px 0; color: #111827; font-weight: 600;">49,00 €</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Status:</td>
                <td style="padding: 8px 0;"><span style="background-color: #10b981; color: white; padding: 4px 12px; border-radius: 20px; font-size: 14px; font-weight: 500;">Bezahlt</span></td>
              </tr>
            </table>
          </div>
          
          <div style="background-color: #fef3c7; padding: 20px; border-radius: 6px; margin-bottom: 25px; border-left: 4px solid #f59e0b;">
            <h3 style="color: #92400e; margin: 0 0 10px 0; font-size: 16px;">📋 Nächste Schritte:</h3>
            <ul style="margin: 0; padding-left: 20px; color: #78350f;">
              <li style="margin-bottom: 8px;">Ihr Energieausweis wird innerhalb von 2-3 Werktagen erstellt</li>
              <li style="margin-bottom: 8px;">Sie erhalten eine separate E-Mail mit dem fertigen Dokument</li>
              <li style="margin-bottom: 8px;">Bei Fragen können Sie uns jederzeit kontaktieren</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; margin: 0 0 10px 0; font-size: 14px;">Bei Fragen stehen wir Ihnen gerne zur Verfügung:</p>
            <p style="color: #2563eb; margin: 0; font-weight: 500;"><EMAIL></p>
          </div>
        </div>
        
        <div style="text-align: center; margin-top: 20px; color: #9ca3af; font-size: 12px;">
          <p style="margin: 0;">© 2024 Energieausweis Service. Alle Rechte vorbehalten.</p>
        </div>
      </div>
    `
  };
};
// Email template for failed payment
const createFailureEmailTemplate = (orderNumber, certificateType, failureReason)=>{
  const certificateTypeNames = {
    'WG/V': 'Wohngebäude Verbrauchsausweis',
    'WG/B': 'Wohngebäude Bedarfsausweis',
    'NWG/V': 'Nichtwohngebäude Verbrauchsausweis'
  };
  const typeName = certificateTypeNames[certificateType] || certificateType;
  return {
    subject: `Zahlungsproblem - Energieausweis ${orderNumber}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #dc2626; margin: 0; font-size: 28px;">Zahlungsproblem</h1>
            <div style="width: 60px; height: 4px; background-color: #ef4444; margin: 15px auto;"></div>
          </div>
          
          <div style="background-color: #fef2f2; padding: 20px; border-radius: 6px; margin-bottom: 25px; border-left: 4px solid #dc2626;">
            <h2 style="color: #b91c1c; margin: 0 0 10px 0; font-size: 20px;">❌ Zahlung fehlgeschlagen</h2>
            <p style="margin: 0; color: #374151; font-size: 16px;">Leider konnte Ihre Zahlung nicht verarbeitet werden.</p>
            ${failureReason ? `<p style="margin: 10px 0 0 0; color: #6b7280; font-size: 14px;">Grund: ${failureReason}</p>` : ''}
          </div>
          
          <div style="margin-bottom: 25px;">
            <h3 style="color: #374151; margin: 0 0 15px 0; font-size: 18px;">Bestelldetails:</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Bestellnummer:</td>
                <td style="padding: 8px 0; color: #111827; font-weight: 600;">${orderNumber}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Zertifikatstyp:</td>
                <td style="padding: 8px 0; color: #111827;">${typeName}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Betrag:</td>
                <td style="padding: 8px 0; color: #111827; font-weight: 600;">49,00 €</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Status:</td>
                <td style="padding: 8px 0;"><span style="background-color: #ef4444; color: white; padding: 4px 12px; border-radius: 20px; font-size: 14px; font-weight: 500;">Fehlgeschlagen</span></td>
              </tr>
            </table>
          </div>
          
          <div style="background-color: #dbeafe; padding: 20px; border-radius: 6px; margin-bottom: 25px; border-left: 4px solid #2563eb;">
            <h3 style="color: #1d4ed8; margin: 0 0 10px 0; font-size: 16px;">💡 Was können Sie tun?</h3>
            <ul style="margin: 0; padding-left: 20px; color: #1e40af;">
              <li style="margin-bottom: 8px;">Überprüfen Sie Ihre Zahlungsdaten und versuchen Sie es erneut</li>
              <li style="margin-bottom: 8px;">Kontaktieren Sie Ihre Bank, falls das Problem weiterhin besteht</li>
              <li style="margin-bottom: 8px;">Verwenden Sie eine alternative Zahlungsmethode</li>
              <li style="margin-bottom: 8px;">Kontaktieren Sie unseren Support für weitere Hilfe</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; margin: 0 0 10px 0; font-size: 14px;">Bei Fragen stehen wir Ihnen gerne zur Verfügung:</p>
            <p style="color: #2563eb; margin: 0; font-weight: 500;"><EMAIL></p>
          </div>
        </div>
        
        <div style="text-align: center; margin-top: 20px; color: #9ca3af; font-size: 12px;">
          <p style="margin: 0;">© 2024 Energieausweis Service. Alle Rechte vorbehalten.</p>
        </div>
      </div>
    `
  };
};
// Email template for disputed payment
const createDisputeEmailTemplate = (orderNumber, certificateType)=>{
  const certificateTypeNames = {
    'WG/V': 'Wohngebäude Verbrauchsausweis',
    'WG/B': 'Wohngebäude Bedarfsausweis',
    'NWG/V': 'Nichtwohngebäude Verbrauchsausweis'
  };
  const typeName = certificateTypeNames[certificateType] || certificateType;
  return {
    subject: `Zahlungsstreitfall - Energieausweis ${orderNumber}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #d97706; margin: 0; font-size: 28px;">Zahlungsstreitfall</h1>
            <div style="width: 60px; height: 4px; background-color: #f59e0b; margin: 15px auto;"></div>
          </div>
          
          <div style="background-color: #fef3c7; padding: 20px; border-radius: 6px; margin-bottom: 25px; border-left: 4px solid #d97706;">
            <h2 style="color: #92400e; margin: 0 0 10px 0; font-size: 20px;">⚠️ Zahlungsstreitfall eingegangen</h2>
            <p style="margin: 0; color: #374151; font-size: 16px;">Wir haben einen Streitfall bezüglich Ihrer Zahlung erhalten und werden diesen umgehend bearbeiten.</p>
          </div>
          
          <div style="margin-bottom: 25px;">
            <h3 style="color: #374151; margin: 0 0 15px 0; font-size: 18px;">Betroffene Bestellung:</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Bestellnummer:</td>
                <td style="padding: 8px 0; color: #111827; font-weight: 600;">${orderNumber}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Zertifikatstyp:</td>
                <td style="padding: 8px 0; color: #111827;">${typeName}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Betrag:</td>
                <td style="padding: 8px 0; color: #111827; font-weight: 600;">49,00 €</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; color: #6b7280; font-weight: 500;">Status:</td>
                <td style="padding: 8px 0;"><span style="background-color: #f59e0b; color: white; padding: 4px 12px; border-radius: 20px; font-size: 14px; font-weight: 500;">Streitfall</span></td>
              </tr>
            </table>
          </div>
          
          <div style="background-color: #f0f9ff; padding: 20px; border-radius: 6px; margin-bottom: 25px; border-left: 4px solid #2563eb;">
            <h3 style="color: #1d4ed8; margin: 0 0 10px 0; font-size: 16px;">📞 Nächste Schritte:</h3>
            <ul style="margin: 0; padding-left: 20px; color: #1e40af;">
              <li style="margin-bottom: 8px;">Unser Support-Team wird sich innerhalb von 24 Stunden bei Ihnen melden</li>
              <li style="margin-bottom: 8px;">Wir werden den Streitfall gemeinsam mit Ihnen klären</li>
              <li style="margin-bottom: 8px;">Alle relevanten Unterlagen werden zur Verfügung gestellt</li>
              <li style="margin-bottom: 8px;">Bei berechtigten Einwänden erfolgt eine vollständige Rückerstattung</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; margin: 0 0 10px 0; font-size: 14px;">Für Rückfragen kontaktieren Sie uns bitte umgehend:</p>
            <p style="color: #2563eb; margin: 0; font-weight: 500;"><EMAIL></p>
          </div>
        </div>
        
        <div style="text-align: center; margin-top: 20px; color: #9ca3af; font-size: 12px;">
          <p style="margin: 0;">© 2024 Energieausweis Service. Alle Rechte vorbehalten.</p>
        </div>
      </div>
    `
  };
};
// Function to send email notifications
const sendEmailNotification = async (to, template)=>{
  if (!resend) {
    console.log('Email service not available - skipping notification');
    return false;
  }
  try {
    const { data, error } = await resend.emails.send({
      from: EMAIL_CONFIG.from,
      to: [
        to
      ],
      subject: template.subject,
      html: template.html,
      reply_to: EMAIL_CONFIG.replyTo
    });
    if (error) {
      console.error('Error sending email:', error);
      return false;
    }
    console.log('Email sent successfully:', data?.id);
    return true;
  } catch (error) {
    console.error('Failed to send email:', error);
    return false;
  }
};
// Function to get user email from certificate
const getUserEmailFromCertificate = async (certificateId)=>{
  try {
    // First try to get email from user table via certificate
    const { data: certData, error: certError } = await supabase.from('energieausweise').select(`
        user_id,
        objektdaten
      `).eq('id', certificateId).single();
    if (certError) {
      console.error('Error fetching certificate:', certError);
      return null;
    }
    // Try to get email from user profile first
    if (certData.user_id) {
      const { data: userData, error: userError } = await supabase.auth.admin.getUserById(certData.user_id);
      if (!userError && userData.user?.email) {
        return userData.user.email;
      }
    }
    // Fallback: try to get email from objektdaten
    if (certData.objektdaten && typeof certData.objektdaten === 'object') {
      const objektdaten = certData.objektdaten;
      if (objektdaten.email && typeof objektdaten.email === 'string') {
        return objektdaten.email;
      }
    }
    console.warn(`No email found for certificate ${certificateId}`);
    return null;
  } catch (error) {
    console.error('Error getting user email:', error);
    return null;
  }
};
// Enhanced webhook event logging with certificate ID extraction
class WebhookEventLogger {
  supabase;
  constructor(supabase){
    this.supabase = supabase;
  }
  // Extract certificate ID from various webhook event structures
  extractCertificateId(event) {
    // Method 1: Direct client_reference_id (most common)
    if (event.data?.object?.client_reference_id) {
      return {
        certificateId: event.data.object.client_reference_id,
        method: 'client_reference_id'
      };
    }
    // Method 2: Metadata certificate_id
    if (event.data?.object?.metadata?.certificate_id) {
      return {
        certificateId: event.data.object.metadata.certificate_id,
        method: 'metadata.certificate_id'
      };
    }
    // Method 3: Payment intent metadata
    if (event.data?.object?.payment_intent?.metadata?.certificate_id) {
      return {
        certificateId: event.data.object.payment_intent.metadata.certificate_id,
        method: 'payment_intent.metadata.certificate_id'
      };
    }
    // Method 4: Charges data (for charge-related events)
    if (event.data?.object?.charges?.data?.[0]?.metadata?.certificate_id) {
      return {
        certificateId: event.data.object.charges.data[0].metadata.certificate_id,
        method: 'charges.data[0].metadata.certificate_id'
      };
    }
    return {
      certificateId: null,
      method: null
    };
  }
  // Log webhook event to database
  async logEvent(event) {
    try {
      const { certificateId, method } = this.extractCertificateId(event);
      const eventLog = {
        stripe_event_id: event.id,
        event_type: event.type,
        certificate_id: certificateId || undefined,
        processing_status: 'pending',
        event_created_at: new Date(event.created * 1000).toISOString(),
        raw_event_data: event,
        certificate_extraction_method: method || undefined
      };
      const { data, error } = await this.supabase.from('stripe_webhook_events').insert(eventLog).select('id').single();
      if (error) {
        console.error('Error logging webhook event:', error);
        return null;
      }
      console.log(`Webhook event logged: ${event.id} (DB ID: ${data.id})`);
      return data.id;
    } catch (error) {
      console.error('Failed to log webhook event:', error);
      return null;
    }
  }
  // Update event processing status
  async updateEventStatus(eventId, status, error) {
    try {
      const updateData = {
        processing_status: status,
        processed_at: new Date().toISOString()
      };
      if (error) {
        updateData.processing_error = error;
      }
      const { error: updateError } = await this.supabase.from('stripe_webhook_events').update(updateData).eq('id', eventId);
      if (updateError) {
        console.error('Error updating event status:', updateError);
      }
    } catch (error) {
      console.error('Failed to update event status:', error);
    }
  }
}
// Initialize webhook event logger
const eventLogger = new WebhookEventLogger(supabase);
Deno.serve(async (request)=>{
  const signature = request.headers.get('Stripe-Signature');
  if (!signature) {
    throw new Error("Missing Stripe signature");
  }
  // First step is to verify the event. The .text() method must be used as the
  // verification relies on the raw request body rather than the parsed JSON.
  const body = await request.text();
  console.log('body: ', body);
  let receivedEvent;
  try {
    receivedEvent = await stripe.webhooks.constructEventAsync(body, signature, webhookSecret, undefined, cryptoProvider);
    console.log(`✅ Signature verification successful ${receivedEvent.id} - ${receivedEvent.type}`);
  } catch (err) {
    console.error(`❌ Signature verification NOT successful`, err.message);
    return new Response(err.message, {
      status: 400
    });
  }
  // Log the webhook event to database
  const logId = await eventLogger.logEvent(receivedEvent);
  // Handle the receivedEvent
  let processingStatus = 'unhandled';
  let processingError;
  try {
    switch(receivedEvent.type){
      case 'checkout.session.completed':
        {
          const session = receivedEvent.data.object;
          console.log(`Payment completed for session: ${session.id}`);
          console.log(`Client reference ID: ${session.client_reference_id}`);
          // Update payment status in database using the client_reference_id (certificate ID)
          if (session.client_reference_id) {
            // First, get the current certificate to check if order_number exists
            const { data: currentCert, error: fetchError } = await supabase.from('energieausweise').select('order_number').eq('id', session.client_reference_id).single();
            if (fetchError) {
              console.error('Error fetching certificate:', fetchError);
              throw new Error(`Failed to fetch certificate: ${fetchError.message}`);
            }
            // Generate order_number if it doesn't exist
            const orderNumber = currentCert?.order_number || `EA-${session.client_reference_id.slice(-8).toUpperCase()}`;
            const { data, error } = await supabase.from('energieausweise').update({
              status: 'paid',
              stripe_checkout_session_id: session.id,
              order_number: orderNumber,
              updated_at: new Date().toISOString()
            }).eq('id', session.client_reference_id).select();
            if (error) {
              console.error('Error updating payment status:', error);
              throw new Error(`Failed to update payment status: ${error.message}`);
            }
            console.log(`Successfully updated certificate status to paid for certificate: ${session.client_reference_id}`);
            console.log(`Order number set to: ${orderNumber}`);
            // Send success email notification
            const userEmail = await getUserEmailFromCertificate(session.client_reference_id);
            if (userEmail && resend) {
              const certificateType = session.metadata?.certificate_type || 'WG/V';
              const emailTemplate = createSuccessEmailTemplate(orderNumber, certificateType);
              const emailSent = await sendEmailNotification(userEmail, emailTemplate);
              if (emailSent) {
                console.log(`Success email sent to: ${userEmail}`);
              } else {
                console.error(`Failed to send success email to: ${userEmail}`);
              }
            } else if (!userEmail) {
              console.warn('No email found for success notification');
            } else {
              console.warn('Email service not available - skipping success email notifications');
            }
          } else {
            console.warn('No client_reference_id found in completed session');
          }
          processingStatus = 'handled';
          break;
        }
      case 'checkout.session.expired':
        {
          const session = receivedEvent.data.object;
          console.log(`Payment session expired: ${session.id}`);
          // Update certificate status to expired if needed
          if (session.client_reference_id) {
            const { data, error } = await supabase.from('energieausweise').update({
              status: 'payment_expired',
              stripe_checkout_session_id: session.id,
              updated_at: new Date().toISOString()
            }).eq('id', session.client_reference_id).select();
            if (error) {
              console.error('Error updating certificate status to expired:', error);
            } else {
              console.log(`Updated certificate status to payment_expired for certificate: ${session.client_reference_id}`);
              // Email notifications removed for expired sessions as they represent
              // likely intentional abandonment rather than payment failures after 24 hours.
              // Status tracking is preserved for admin dashboard analytics.
              console.log('Session expired - no email notifications sent (intentional abandonment assumed)');
            }
          }
          processingStatus = 'handled';
          break;
        }
      case 'payment_intent.succeeded':
        {
          const paymentIntent = receivedEvent.data.object;
          console.log(`Payment intent succeeded: ${paymentIntent.id}`);
          processingStatus = 'handled';
          break;
        }
      case 'async_payment_succeeded':
        {
          const session = receivedEvent.data.object;
          console.log(`Async payment succeeded for session: ${session.id}`);
          console.log(`Client reference ID: ${session.client_reference_id}`);
          // Handle async payment success (e.g., SEPA, bank transfers)
          if (session.client_reference_id) {
            // First, get the current certificate to check if order_number exists
            const { data: currentCert, error: fetchError } = await supabase.from('energieausweise').select('order_number').eq('id', session.client_reference_id).single();
            if (fetchError) {
              console.error('Error fetching certificate for async payment:', fetchError);
              throw new Error(`Failed to fetch certificate: ${fetchError.message}`);
            }
            // Generate order_number if it doesn't exist
            const orderNumber = currentCert?.order_number || `EA-${session.client_reference_id.slice(-8).toUpperCase()}`;
            const { data, error } = await supabase.from('energieausweise').update({
              status: 'paid',
              stripe_checkout_session_id: session.id,
              order_number: orderNumber,
              updated_at: new Date().toISOString()
            }).eq('id', session.client_reference_id).select();
            if (error) {
              console.error('Error updating certificate status for async payment:', error);
              throw new Error(`Failed to update certificate status: ${error.message}`);
            }
            console.log(`Successfully updated certificate status to paid for async payment certificate: ${session.client_reference_id}`);
            console.log(`Order number set to: ${orderNumber}`);
            // Send success email notification for async payment
            const userEmail = await getUserEmailFromCertificate(session.client_reference_id);
            if (userEmail && resend) {
              const certificateType = session.metadata?.certificate_type || 'WG/V';
              const emailTemplate = createSuccessEmailTemplate(orderNumber, certificateType);
              const emailSent = await sendEmailNotification(userEmail, emailTemplate);
              if (emailSent) {
                console.log(`Async payment success email sent to: ${userEmail}`);
              } else {
                console.error(`Failed to send async payment success email to: ${userEmail}`);
              }
            } else if (!userEmail) {
              console.warn('No email found for async payment success notification');
            } else {
              console.warn('Email service not available - skipping email notifications for async payment');
            }
          } else {
            console.warn('No client_reference_id found in async payment succeeded session');
          }
          processingStatus = 'handled';
          break;
        }
      case 'charge.failed':
        {
          const charge = receivedEvent.data.object;
          console.log(`Charge failed: ${charge.id}`);
          // Try to find the certificate ID from metadata
          const certificateId = charge.metadata?.certificate_id;
          if (certificateId) {
            const { error } = await supabase.from('energieausweise').update({
              status: 'payment_failed',
              updated_at: new Date().toISOString()
            }).eq('id', certificateId).select();
            if (error) {
              console.error('Error updating certificate status for failed charge:', error);
            } else {
              console.log(`Updated certificate status to payment_failed for certificate: ${certificateId}`);
              // Send failure email notification
              const userEmail = await getUserEmailFromCertificate(certificateId);
              if (userEmail && resend) {
                // Get certificate details for email
                const { data: certData } = await supabase.from('energieausweise').select('certificate_type, order_number').eq('id', certificateId).single();
                const certificateType = certData?.certificate_type || 'WG/V';
                const orderNumber = certData?.order_number || `EA-${certificateId.slice(-8).toUpperCase()}`;
                const failureReason = charge.failure_message || 'Unbekannter Fehler';
                const emailTemplate = createFailureEmailTemplate(orderNumber, certificateType, failureReason);
                const emailSent = await sendEmailNotification(userEmail, emailTemplate);
                if (emailSent) {
                  console.log(`Failure email sent to: ${userEmail}`);
                } else {
                  console.error(`Failed to send failure email to: ${userEmail}`);
                }
              } else if (!userEmail) {
                console.warn('No email found for failure notification');
              } else {
                console.warn('Email service not available - skipping failure email notifications');
              }
            }
          } else {
            console.warn('No certificate_id found in charge metadata for failed charge');
          }
          processingStatus = 'handled';
          break;
        }
      case 'charge.dispute.created':
        {
          const dispute = receivedEvent.data.object;
          console.log(`Dispute created for charge: ${dispute.charge}`);
          // Try to find the certificate ID from the charge metadata
          try {
            const charge = await stripe.charges.retrieve(dispute.charge);
            const certificateId = charge.metadata?.certificate_id;
            if (certificateId) {
              const { error } = await supabase.from('energieausweise').update({
                status: 'payment_disputed',
                updated_at: new Date().toISOString()
              }).eq('id', certificateId).select();
              if (error) {
                console.error('Error updating certificate status for dispute:', error);
              } else {
                console.log(`Updated certificate status to payment_disputed for certificate: ${certificateId}`);
                // Send dispute email notification
                const userEmail = await getUserEmailFromCertificate(certificateId);
                if (userEmail && resend) {
                  // Get certificate details for email
                  const { data: certData } = await supabase.from('energieausweise').select('certificate_type, order_number').eq('id', certificateId).single();
                  const certificateType = certData?.certificate_type || 'WG/V';
                  const orderNumber = certData?.order_number || `EA-${certificateId.slice(-8).toUpperCase()}`;
                  const emailTemplate = createDisputeEmailTemplate(orderNumber, certificateType);
                  const emailSent = await sendEmailNotification(userEmail, emailTemplate);
                  if (emailSent) {
                    console.log(`Dispute email sent to: ${userEmail}`);
                  } else {
                    console.error(`Failed to send dispute email to: ${userEmail}`);
                  }
                } else if (!userEmail) {
                  console.warn('No email found for dispute notification');
                } else {
                  console.warn('Email service not available - skipping dispute email notifications');
                }
              }
            } else {
              console.warn('No certificate_id found in charge metadata for dispute');
            }
          } catch (chargeError) {
            console.error('Error retrieving charge for dispute:', chargeError);
          }
          processingStatus = 'handled';
          break;
        }
      case 'invoice.payment_failed':
        {
          const invoice = receivedEvent.data.object;
          console.log(`Invoice payment failed: ${invoice.id}`);
          // Try to find the certificate ID from metadata
          const certificateId = invoice.metadata?.certificate_id;
          if (certificateId) {
            const { error } = await supabase.from('energieausweise').update({
              status: 'payment_failed',
              updated_at: new Date().toISOString()
            }).eq('id', certificateId).select();
            if (error) {
              console.error('Error updating certificate status for failed invoice:', error);
            } else {
              console.log(`Updated certificate status to payment_failed for certificate: ${certificateId}`);
              // Send failure email notification for invoice
              const userEmail = await getUserEmailFromCertificate(certificateId);
              if (userEmail && resend) {
                // Get certificate details for email
                const { data: certData } = await supabase.from('energieausweise').select('certificate_type, order_number').eq('id', certificateId).single();
                const certificateType = certData?.certificate_type || 'WG/V';
                const orderNumber = certData?.order_number || `EA-${certificateId.slice(-8).toUpperCase()}`;
                const emailTemplate = createFailureEmailTemplate(orderNumber, certificateType, 'Rechnungszahlung fehlgeschlagen');
                const emailSent = await sendEmailNotification(userEmail, emailTemplate);
                if (emailSent) {
                  console.log(`Invoice failure email sent to: ${userEmail}`);
                } else {
                  console.error(`Failed to send invoice failure email to: ${userEmail}`);
                }
              } else if (!userEmail) {
                console.warn('No email found for invoice failure notification');
              } else {
                console.warn('Email service not available - skipping invoice failure email notifications');
              }
            }
          } else {
            console.warn('No certificate_id found in invoice metadata for failed payment');
          }
          processingStatus = 'handled';
          break;
        }
      default:
        console.log(`Unhandled event type: ${receivedEvent.type}`);
        processingStatus = 'unhandled';
    }
  } catch (error) {
    console.error(`Error processing webhook event ${receivedEvent.id}:`, error);
    processingStatus = 'error';
    processingError = error.message || 'Unknown error';
  }
  // Update the event log with processing status
  if (logId) {
    await eventLogger.updateEventStatus(logId, processingStatus, processingError);
  }
  console.log(`Webhook ${receivedEvent.id} processed with status: ${processingStatus}`);
  return new Response(JSON.stringify({
    received: true,
    status: processingStatus
  }), {
    headers: {
      "Content-Type": "application/json"
    },
    status: 200
  });
});
