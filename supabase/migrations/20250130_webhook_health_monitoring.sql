-- Create webhook health monitoring table
CREATE TABLE IF NOT EXISTS webhook_health_checks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    check_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    jwt_verification_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    webhook_function_accessible BOOLEAN NOT NULL DEFAULT TRUE,
    last_successful_webhook TIMESTAMPTZ,
    failed_webhooks_count_24h INTEGER NOT NULL DEFAULT 0,
    alert_sent BOOLEAN NOT NULL DEFAULT FALSE,
    error_details TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for efficient querying
CREATE INDEX IF NOT EXISTS idx_webhook_health_checks_timestamp 
ON webhook_health_checks(check_timestamp DESC);

-- Create index for alert tracking
CREATE INDEX IF NOT EXISTS idx_webhook_health_checks_alerts 
ON webhook_health_checks(alert_sent, check_timestamp DESC);

-- Add RLS policy for admin access only
ALTER TABLE webhook_health_checks ENABLE ROW LEVEL SECURITY;

-- Policy: Only admins can read webhook health checks
CREATE POLICY "Admin can read webhook health checks" ON webhook_health_checks
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_user_meta_data->>'role' = 'admin'
        )
    );

-- Policy: Service role can insert health checks (for the monitoring function)
CREATE POLICY "Service role can insert health checks" ON webhook_health_checks
    FOR INSERT WITH CHECK (true);

-- Create a view for recent health status
CREATE OR REPLACE VIEW webhook_health_status AS
SELECT 
    id,
    check_timestamp,
    jwt_verification_enabled,
    webhook_function_accessible,
    last_successful_webhook,
    failed_webhooks_count_24h,
    alert_sent,
    error_details,
    CASE 
        WHEN jwt_verification_enabled THEN 'CRITICAL'
        WHEN NOT webhook_function_accessible THEN 'CRITICAL'
        WHEN failed_webhooks_count_24h >= 3 THEN 'WARNING'
        ELSE 'HEALTHY'
    END as health_status,
    CASE 
        WHEN jwt_verification_enabled THEN 'JWT verification is enabled - webhooks will fail'
        WHEN NOT webhook_function_accessible THEN 'Webhook function is not accessible'
        WHEN failed_webhooks_count_24h >= 3 THEN 'Multiple webhook failures detected'
        ELSE 'All systems operational'
    END as status_message
FROM webhook_health_checks
ORDER BY check_timestamp DESC;

-- Grant access to the view
GRANT SELECT ON webhook_health_status TO authenticated;

-- Create a function to get the latest health status
CREATE OR REPLACE FUNCTION get_latest_webhook_health()
RETURNS TABLE (
    health_status TEXT,
    status_message TEXT,
    check_timestamp TIMESTAMPTZ,
    jwt_verification_enabled BOOLEAN,
    webhook_function_accessible BOOLEAN,
    failed_webhooks_count_24h INTEGER,
    last_successful_webhook TIMESTAMPTZ
) 
LANGUAGE SQL
SECURITY DEFINER
AS $$
    SELECT 
        health_status,
        status_message,
        check_timestamp,
        jwt_verification_enabled,
        webhook_function_accessible,
        failed_webhooks_count_24h,
        last_successful_webhook
    FROM webhook_health_status 
    LIMIT 1;
$$;

-- Create a function to check if webhooks are healthy
CREATE OR REPLACE FUNCTION are_webhooks_healthy()
RETURNS BOOLEAN
LANGUAGE SQL
SECURITY DEFINER
AS $$
    SELECT 
        CASE 
            WHEN health_status IN ('CRITICAL', 'WARNING') THEN FALSE
            ELSE TRUE
        END
    FROM webhook_health_status 
    LIMIT 1;
$$;

-- Add comment explaining the table purpose
COMMENT ON TABLE webhook_health_checks IS 'Stores webhook health monitoring data to track JWT verification status and webhook failures';
COMMENT ON COLUMN webhook_health_checks.jwt_verification_enabled IS 'Whether JWT verification is enabled (breaks webhooks if true)';
COMMENT ON COLUMN webhook_health_checks.webhook_function_accessible IS 'Whether the webhook function is accessible without JWT';
COMMENT ON COLUMN webhook_health_checks.failed_webhooks_count_24h IS 'Number of failed webhooks in the last 24 hours';
COMMENT ON COLUMN webhook_health_checks.alert_sent IS 'Whether an alert email was sent for this check';
